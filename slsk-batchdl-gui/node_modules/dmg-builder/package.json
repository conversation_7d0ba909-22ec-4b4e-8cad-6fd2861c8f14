{"name": "dmg-builder", "version": "24.13.3", "main": "out/dmgUtil.js", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/dmg-builder"}, "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "files": ["out", "templates", "vendor"], "dependencies": {"fs-extra": "^10.1.0", "iconv-lite": "^0.6.2", "js-yaml": "^4.1.0", "app-builder-lib": "24.13.3", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4"}, "optionalDependencies": {"dmg-license": "^1.0.11"}, "devDependencies": {"@types/fs-extra": "9.0.13", "@types/js-yaml": "4.0.3", "temp-file": "3.4.0"}, "typings": "./out/dmg.d.ts"}