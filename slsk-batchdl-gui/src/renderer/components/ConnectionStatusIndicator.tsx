import React, { useState } from 'react'
import {
  Box,
  Chip,
  Popover,
  Typo<PERSON>,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  But<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mui/material'
import {
  CheckCircle as ConnectedIcon,
  Error as DisconnectedIcon,
  Warning as WarningIcon,
  Circle as DotIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material'

interface ConnectionStatus {
  soulseek: {
    status: 'connected' | 'connecting' | 'disconnected' | 'error'
    username?: string
    connectedSince?: Date
    lastError?: string
  }
  spotify: {
    status: 'connected' | 'disconnected' | 'error'
    lastValidated?: Date
    hasCredentials: boolean
  }
  youtube: {
    status: 'connected' | 'disconnected' | 'error'
    lastValidated?: Date
    hasCredentials: boolean
  }
}

interface ConnectionStatusIndicatorProps {
  status: ConnectionStatus
  onOpenSettings: () => void
}

const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({
  status,
  onOpenSettings,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null)

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const getOverallStatus = () => {
    const soulseekConnected = status.soulseek.status === 'connected'
    const hasErrors = Object.values(status).some(s => s.status === 'error')
    const isConnecting = status.soulseek.status === 'connecting'

    if (hasErrors) return 'error'
    if (isConnecting) return 'connecting'
    if (soulseekConnected) return 'connected'
    return 'disconnected'
  }

  const getOverallStatusText = () => {
    const overallStatus = getOverallStatus()
    switch (overallStatus) {
      case 'connected':
        return 'Connected'
      case 'connecting':
        return 'Connecting...'
      case 'error':
        return 'Connection Error'
      case 'disconnected':
      default:
        return 'Disconnected'
    }
  }

  const getOverallStatusColor = () => {
    const overallStatus = getOverallStatus()
    switch (overallStatus) {
      case 'connected':
        return 'success'
      case 'connecting':
        return 'warning'
      case 'error':
        return 'error'
      case 'disconnected':
      default:
        return 'default'
    }
  }

  const getServiceIcon = (serviceStatus: string) => {
    switch (serviceStatus) {
      case 'connected':
        return <ConnectedIcon color="success" fontSize="small" />
      case 'connecting':
        return <WarningIcon color="warning" fontSize="small" />
      case 'error':
        return <DisconnectedIcon color="error" fontSize="small" />
      case 'disconnected':
      default:
        return <DotIcon color="disabled" fontSize="small" />
    }
  }

  const getServiceStatusText = (service: string, serviceData: any) => {
    switch (serviceData.status) {
      case 'connected':
        if (service === 'soulseek' && serviceData.username) {
          return `Connected as ${serviceData.username}`
        }
        return 'Connected'
      case 'connecting':
        return 'Connecting...'
      case 'error':
        return serviceData.lastError || 'Connection error'
      case 'disconnected':
      default:
        if (service !== 'soulseek' && !serviceData.hasCredentials) {
          return 'No credentials configured'
        }
        return 'Disconnected'
    }
  }

  const formatDuration = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (days > 0) return `${days}d ${hours % 24}h`
    if (hours > 0) return `${hours}h ${minutes % 60}m`
    return `${minutes}m`
  }

  const open = Boolean(anchorEl)

  return (
    <>
      <Chip
        icon={getServiceIcon(getOverallStatus())}
        label={getOverallStatusText()}
        color={getOverallStatusColor()}
        variant="outlined"
        size="small"
        onClick={handleClick}
        sx={{ cursor: 'pointer' }}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
      >
        <Box sx={{ p: 2, minWidth: 300 }}>
          <Typography variant="h6" gutterBottom>
            Connection Status
          </Typography>

          <List dense>
            {/* Soulseek Status */}
            <ListItem>
              <ListItemIcon>
                {getServiceIcon(status.soulseek.status)}
              </ListItemIcon>
              <ListItemText
                primary="Soulseek"
                secondary={
                  <Box>
                    <Typography variant="body2">
                      {getServiceStatusText('soulseek', status.soulseek)}
                    </Typography>
                    {status.soulseek.status === 'connected' && status.soulseek.connectedSince && (
                      <Typography variant="caption" color="text.secondary">
                        Connected for {formatDuration(status.soulseek.connectedSince)}
                      </Typography>
                    )}
                  </Box>
                }
              />
            </ListItem>

            <Divider />

            {/* Spotify Status */}
            <ListItem>
              <ListItemIcon>
                {getServiceIcon(status.spotify.status)}
              </ListItemIcon>
              <ListItemText
                primary="Spotify API"
                secondary={
                  <Box>
                    <Typography variant="body2">
                      {getServiceStatusText('spotify', status.spotify)}
                    </Typography>
                    {status.spotify.lastValidated && (
                      <Typography variant="caption" color="text.secondary">
                        Last validated: {status.spotify.lastValidated.toLocaleDateString()}
                      </Typography>
                    )}
                  </Box>
                }
              />
            </ListItem>

            <Divider />

            {/* YouTube Status */}
            <ListItem>
              <ListItemIcon>
                {getServiceIcon(status.youtube.status)}
              </ListItemIcon>
              <ListItemText
                primary="YouTube API"
                secondary={
                  <Box>
                    <Typography variant="body2">
                      {getServiceStatusText('youtube', status.youtube)}
                    </Typography>
                    {status.youtube.lastValidated && (
                      <Typography variant="caption" color="text.secondary">
                        Last validated: {status.youtube.lastValidated.toLocaleDateString()}
                      </Typography>
                    )}
                  </Box>
                }
              />
            </ListItem>
          </List>

          {/* Error Alert */}
          {getOverallStatus() === 'error' && (
            <Alert severity="error" sx={{ mt: 2 }}>
              <Typography variant="body2">
                One or more connections have errors. Check the settings to resolve issues.
              </Typography>
            </Alert>
          )}

          {/* No Credentials Alert */}
          {(!status.spotify.hasCredentials || !status.youtube.hasCredentials) && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Configure API credentials in settings to enable additional features.
              </Typography>
            </Alert>
          )}

          <Button
            fullWidth
            variant="outlined"
            startIcon={<SettingsIcon />}
            onClick={() => {
              onOpenSettings()
              handleClose()
            }}
            sx={{ mt: 2 }}
          >
            Open Connection Settings
          </Button>
        </Box>
      </Popover>
    </>
  )
}

export default ConnectionStatusIndicator
