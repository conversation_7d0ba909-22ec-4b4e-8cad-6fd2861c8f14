import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Chip,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Tooltip,
  IconButton,
  OutlinedInput,
  SelectChangeEvent,
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  Help as HelpIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material'

import { FileConditions } from '../types/slsk'

interface FileConditionsFormProps {
  title: string
  conditions: FileConditions
  onChange: (conditions: FileConditions) => void
  isNecessary?: boolean
}

const FileConditionsForm: React.FC<FileConditionsFormProps> = ({
  title,
  conditions,
  onChange,
  isNecessary = false,
}) => {
  const [newFormat, setNewFormat] = useState('')
  const [newBannedUser, setNewBannedUser] = useState('')

  const handleChange = (field: keyof FileConditions, value: any) => {
    onChange({
      ...conditions,
      [field]: value,
    })
  }

  const handleFormatAdd = () => {
    if (newFormat.trim() && !conditions.formats?.includes(newFormat.trim().toLowerCase())) {
      const formats = conditions.formats || []
      handleChange('formats', [...formats, newFormat.trim().toLowerCase()])
      setNewFormat('')
    }
  }

  const handleFormatRemove = (format: string) => {
    const formats = conditions.formats?.filter(f => f !== format) || []
    handleChange('formats', formats)
  }

  const handleBannedUserAdd = () => {
    if (newBannedUser.trim() && !conditions.bannedUsers?.includes(newBannedUser.trim())) {
      const bannedUsers = conditions.bannedUsers || []
      handleChange('bannedUsers', [...bannedUsers, newBannedUser.trim()])
      setNewBannedUser('')
    }
  }

  const handleBannedUserRemove = (user: string) => {
    const bannedUsers = conditions.bannedUsers?.filter(u => u !== user) || []
    handleChange('bannedUsers', bannedUsers)
  }

  const commonFormats = ['mp3', 'flac', 'ogg', 'm4a', 'wav', 'aac', 'wma']

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
          <Typography variant="h6">
            {title}
          </Typography>
          <Tooltip title={isNecessary ? 
            "Files must meet ALL necessary conditions to be downloaded" : 
            "Preferred conditions are used for ranking search results"
          }>
            <IconButton size="small">
              <HelpIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>

        <Alert severity={isNecessary ? "warning" : "info"} sx={{ mb: 2 }}>
          {isNecessary ? 
            "Files must meet ALL necessary conditions to be considered for download." :
            "Preferred conditions help rank search results. Files not meeting these may still be downloaded."
          }
        </Alert>

        <Grid container spacing={2}>
          {/* File Formats */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              File Formats
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
              {commonFormats.map(format => (
                <Chip
                  key={format}
                  label={format.toUpperCase()}
                  clickable
                  color={conditions.formats?.includes(format) ? 'primary' : 'default'}
                  variant={conditions.formats?.includes(format) ? 'filled' : 'outlined'}
                  onClick={() => {
                    const formats = conditions.formats || []
                    if (formats.includes(format)) {
                      handleFormatRemove(format)
                    } else {
                      handleChange('formats', [...formats, format])
                    }
                  }}
                />
              ))}
            </Box>

            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', mb: 2 }}>
              <TextField
                size="small"
                label="Custom format"
                value={newFormat}
                onChange={(e) => setNewFormat(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleFormatAdd()}
                placeholder="e.g., opus"
              />
              <IconButton onClick={handleFormatAdd} color="primary">
                <AddIcon />
              </IconButton>
            </Box>

            {conditions.formats && conditions.formats.length > 0 && (
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 2 }}>
                {conditions.formats.map(format => (
                  <Chip
                    key={format}
                    label={format}
                    onDelete={() => handleFormatRemove(format)}
                    color="primary"
                    size="small"
                  />
                ))}
              </Box>
            )}
          </Grid>

          {/* Bitrate Settings */}
          <Grid item xs={6}>
            <TextField
              fullWidth
              type="number"
              label="Minimum Bitrate (kbps)"
              value={conditions.minBitrate || ''}
              onChange={(e) => handleChange('minBitrate', parseInt(e.target.value) || undefined)}
              placeholder="e.g., 128"
            />
          </Grid>

          <Grid item xs={6}>
            <TextField
              fullWidth
              type="number"
              label="Maximum Bitrate (kbps)"
              value={conditions.maxBitrate || ''}
              onChange={(e) => handleChange('maxBitrate', parseInt(e.target.value) || undefined)}
              placeholder="e.g., 320"
            />
          </Grid>

          {/* Sample Rate Settings */}
          <Grid item xs={6}>
            <TextField
              fullWidth
              type="number"
              label="Minimum Sample Rate (Hz)"
              value={conditions.minSampleRate || ''}
              onChange={(e) => handleChange('minSampleRate', parseInt(e.target.value) || undefined)}
              placeholder="e.g., 44100"
            />
          </Grid>

          <Grid item xs={6}>
            <TextField
              fullWidth
              type="number"
              label="Maximum Sample Rate (Hz)"
              value={conditions.maxSampleRate || ''}
              onChange={(e) => handleChange('maxSampleRate', parseInt(e.target.value) || undefined)}
              placeholder="e.g., 192000"
            />
          </Grid>

          {/* Bit Depth Settings */}
          <Grid item xs={6}>
            <TextField
              fullWidth
              type="number"
              label="Minimum Bit Depth"
              value={conditions.minBitDepth || ''}
              onChange={(e) => handleChange('minBitDepth', parseInt(e.target.value) || undefined)}
              placeholder="e.g., 16"
            />
          </Grid>

          <Grid item xs={6}>
            <TextField
              fullWidth
              type="number"
              label="Maximum Bit Depth"
              value={conditions.maxBitDepth || ''}
              onChange={(e) => handleChange('maxBitDepth', parseInt(e.target.value) || undefined)}
              placeholder="e.g., 24"
            />
          </Grid>

          {/* Length Tolerance */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              type="number"
              label="Length Tolerance (seconds)"
              value={conditions.lengthTolerance || ''}
              onChange={(e) => handleChange('lengthTolerance', parseInt(e.target.value) || undefined)}
              placeholder="e.g., 3"
              helperText="How much the file length can differ from the expected length"
            />
          </Grid>

          {/* Matching Options */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Matching Options
            </Typography>
            
            <FormControlLabel
              control={
                <Switch
                  checked={conditions.strictTitle || false}
                  onChange={(e) => handleChange('strictTitle', e.target.checked)}
                />
              }
              label="Strict title matching"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={conditions.strictArtist || false}
                  onChange={(e) => handleChange('strictArtist', e.target.checked)}
                />
              }
              label="Strict artist matching"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={conditions.strictAlbum || false}
                  onChange={(e) => handleChange('strictAlbum', e.target.checked)}
                />
              }
              label="Strict album matching"
            />
          </Grid>

          {/* Banned Users */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Banned Users
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', mb: 1 }}>
              <TextField
                size="small"
                label="Username to ban"
                value={newBannedUser}
                onChange={(e) => setNewBannedUser(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleBannedUserAdd()}
                placeholder="Enter username"
                sx={{ flexGrow: 1 }}
              />
              <IconButton onClick={handleBannedUserAdd} color="primary">
                <AddIcon />
              </IconButton>
            </Box>

            {conditions.bannedUsers && conditions.bannedUsers.length > 0 && (
              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                {conditions.bannedUsers.map(user => (
                  <Chip
                    key={user}
                    label={user}
                    onDelete={() => handleBannedUserRemove(user)}
                    color="error"
                    size="small"
                  />
                ))}
              </Box>
            )}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default FileConditionsForm
