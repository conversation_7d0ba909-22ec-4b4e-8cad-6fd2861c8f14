import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  FormControlLabel,
  Checkbox,
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  BugReport as BugIcon,
  Send as SendIcon,
  Copy as CopyIcon,
  CheckCircle as SolutionIcon,
} from '@mui/icons-material'

interface ErrorDetails {
  id: string
  timestamp: Date
  level: 'error' | 'warning' | 'info'
  message: string
  source: string
  stackTrace?: string
  context?: Record<string, any>
  userAction?: string
  suggestions?: string[]
}

interface ErrorReportDialogProps {
  open: boolean
  onClose: () => void
  error: ErrorDetails | null
  onSendReport: (report: any) => void
}

const ErrorReportDialog: React.FC<ErrorReportDialogProps> = ({
  open,
  onClose,
  error,
  onSendReport,
}) => {
  const [userDescription, setUserDescription] = useState('')
  const [includeSystemInfo, setIncludeSystemInfo] = useState(true)
  const [includeLogs, setIncludeLogs] = useState(true)
  const [reportSent, setReportSent] = useState(false)

  const handleSendReport = () => {
    const report = {
      error,
      userDescription,
      includeSystemInfo,
      includeLogs,
      timestamp: new Date(),
    }
    
    onSendReport(report)
    setReportSent(true)
    
    // Reset form after a delay
    setTimeout(() => {
      setReportSent(false)
      setUserDescription('')
      onClose()
    }, 2000)
  }

  const handleCopyError = () => {
    if (error) {
      const errorText = `
Error: ${error.message}
Source: ${error.source}
Timestamp: ${error.timestamp.toISOString()}
${error.stackTrace ? `Stack Trace:\n${error.stackTrace}` : ''}
${error.context ? `Context:\n${JSON.stringify(error.context, null, 2)}` : ''}
      `.trim()
      
      navigator.clipboard.writeText(errorText)
    }
  }

  const getErrorSuggestions = (error: ErrorDetails | null) => {
    if (!error) return []

    // Common error patterns and their solutions
    const suggestions: Record<string, string[]> = {
      'connection': [
        'Check your internet connection',
        'Verify Soulseek credentials in Settings',
        'Try restarting the application',
        'Check if Soulseek servers are online',
      ],
      'authentication': [
        'Verify your username and password',
        'Check if your account is active',
        'Try logging in through the official Soulseek client',
        'Reset your password if necessary',
      ],
      'download': [
        'Check available disk space',
        'Verify the output directory exists and is writable',
        'Try downloading a different file',
        'Check if the user is still online',
      ],
      'search': [
        'Try a different search query',
        'Check your search filters',
        'Verify your connection to Soulseek',
        'Try searching for more common terms',
      ],
      'api': [
        'Check your API credentials in Settings',
        'Verify your internet connection',
        'Check if the service is experiencing issues',
        'Try refreshing your API tokens',
      ],
    }

    return suggestions[error.source] || [
      'Try restarting the application',
      'Check the logs for more details',
      'Contact support if the issue persists',
    ]
  }

  if (!error) return null

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ErrorIcon color="error" />
          <Typography variant="h6">Error Report</Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {reportSent ? (
          <Alert severity="success" sx={{ mb: 2 }}>
            <Typography variant="h6" gutterBottom>
              Report Sent Successfully!
            </Typography>
            <Typography>
              Thank you for reporting this issue. We'll investigate and work on a fix.
            </Typography>
          </Alert>
        ) : (
          <>
            {/* Error Summary */}
            <Alert severity={error.level} sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                {error.message}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                <Chip label={error.source} size="small" />
                <Chip label={error.timestamp.toLocaleString()} size="small" />
              </Box>
            </Alert>

            {/* Suggested Solutions */}
            <Accordion defaultExpanded sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SolutionIcon color="primary" />
                  <Typography variant="h6">Suggested Solutions</Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {getErrorSuggestions(error).map((suggestion, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <InfoIcon color="primary" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary={suggestion} />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>

            {/* Error Details */}
            <Accordion sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <BugIcon />
                  <Typography variant="h6">Technical Details</Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ mb: 2 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<CopyIcon />}
                    onClick={handleCopyError}
                  >
                    Copy Error Details
                  </Button>
                </Box>

                {error.stackTrace && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Stack Trace:
                    </Typography>
                    <Box
                      component="pre"
                      sx={{
                        bgcolor: 'grey.100',
                        p: 1,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        overflow: 'auto',
                        maxHeight: 200,
                      }}
                    >
                      {error.stackTrace}
                    </Box>
                  </Box>
                )}

                {error.context && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Context:
                    </Typography>
                    <Box
                      component="pre"
                      sx={{
                        bgcolor: 'grey.100',
                        p: 1,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        overflow: 'auto',
                        maxHeight: 200,
                      }}
                    >
                      {JSON.stringify(error.context, null, 2)}
                    </Box>
                  </Box>
                )}

                {error.userAction && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      User Action:
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {error.userAction}
                    </Typography>
                  </Box>
                )}
              </AccordionDetails>
            </Accordion>

            {/* User Report */}
            <Accordion sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Send Error Report</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Help us improve the application by sending this error report. Your feedback is valuable!
                </Typography>

                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Describe what you were doing when this error occurred (optional)"
                  value={userDescription}
                  onChange={(e) => setUserDescription(e.target.value)}
                  sx={{ mb: 2 }}
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={includeSystemInfo}
                      onChange={(e) => setIncludeSystemInfo(e.target.checked)}
                    />
                  }
                  label="Include system information"
                />

                <FormControlLabel
                  control={
                    <Checkbox
                      checked={includeLogs}
                      onChange={(e) => setIncludeLogs(e.target.checked)}
                    />
                  }
                  label="Include recent logs"
                />
              </AccordionDetails>
            </Accordion>
          </>
        )}
      </DialogContent>

      <DialogActions>
        {!reportSent && (
          <>
            <Button onClick={onClose}>
              Close
            </Button>
            <Button
              variant="contained"
              startIcon={<SendIcon />}
              onClick={handleSendReport}
            >
              Send Report
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default ErrorReportDialog
