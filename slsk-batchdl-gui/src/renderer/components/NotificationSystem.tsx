import React, { useState, useEffect, create<PERSON>ontext, useContext, ReactNode } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert,
  <PERSON>ertT<PERSON>le,
  Button,
  Box,
  IconButton,
  Slide,
  SlideProps,
} from '@mui/material'
import {
  Close as CloseIcon,
  BugReport as BugReportIcon,
} from '@mui/icons-material'

import ErrorReportDialog from './ErrorReportDialog'

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  errorDetails?: any
  persistent?: boolean
}

interface NotificationContextType {
  showNotification: (notification: Omit<Notification, 'id'>) => void
  showSuccess: (message: string, title?: string) => void
  showError: (message: string, title?: string, errorDetails?: any) => void
  showWarning: (message: string, title?: string) => void
  showInfo: (message: string, title?: string) => void
  clearNotifications: () => void
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export const useNotifications = () => {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

function SlideTransition(props: SlideProps) {
  return <Slide {...props} direction="up" />
}

interface NotificationProviderProps {
  children: ReactNode
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [errorReportOpen, setErrorReportOpen] = useState(false)
  const [currentError, setCurrentError] = useState<any>(null)

  const showNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newNotification: Notification = {
      id,
      duration: 6000,
      ...notification,
    }

    setNotifications(prev => [...prev, newNotification])

    // Auto-remove notification after duration (unless persistent)
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }
  }

  const showSuccess = (message: string, title?: string) => {
    showNotification({
      type: 'success',
      title,
      message,
    })
  }

  const showError = (message: string, title?: string, errorDetails?: any) => {
    showNotification({
      type: 'error',
      title: title || 'Error',
      message,
      errorDetails,
      persistent: true,
      action: errorDetails ? {
        label: 'Report',
        onClick: () => {
          setCurrentError({
            id: Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            level: 'error' as const,
            message,
            source: errorDetails?.source || 'unknown',
            stackTrace: errorDetails?.stack,
            context: errorDetails?.context,
            userAction: errorDetails?.userAction,
          })
          setErrorReportOpen(true)
        }
      } : undefined,
    })
  }

  const showWarning = (message: string, title?: string) => {
    showNotification({
      type: 'warning',
      title,
      message,
    })
  }

  const showInfo = (message: string, title?: string) => {
    showNotification({
      type: 'info',
      title,
      message,
    })
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const clearNotifications = () => {
    setNotifications([])
  }

  const handleErrorReport = (report: any) => {
    console.log('Error report submitted:', report)
    showSuccess('Error report sent successfully. Thank you for helping us improve!')
  }

  const contextValue: NotificationContextType = {
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    clearNotifications,
  }

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Render notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={notification.id}
          open={true}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          TransitionComponent={SlideTransition}
          sx={{
            position: 'fixed',
            bottom: 16 + (index * 80), // Stack notifications
            right: 16,
            zIndex: 9999,
          }}
        >
          <Alert
            severity={notification.type}
            variant="filled"
            sx={{ minWidth: 300, maxWidth: 500 }}
            action={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {notification.action && (
                  <Button
                    color="inherit"
                    size="small"
                    onClick={notification.action.onClick}
                    startIcon={notification.errorDetails ? <BugReportIcon /> : undefined}
                  >
                    {notification.action.label}
                  </Button>
                )}
                <IconButton
                  size="small"
                  color="inherit"
                  onClick={() => removeNotification(notification.id)}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            }
          >
            {notification.title && (
              <AlertTitle>{notification.title}</AlertTitle>
            )}
            {notification.message}
          </Alert>
        </Snackbar>
      ))}

      {/* Error Report Dialog */}
      <ErrorReportDialog
        open={errorReportOpen}
        onClose={() => {
          setErrorReportOpen(false)
          setCurrentError(null)
        }}
        error={currentError}
        onSendReport={handleErrorReport}
      />
    </NotificationContext.Provider>
  )
}

// Hook for common error patterns
export const useErrorHandler = () => {
  const { showError, showWarning } = useNotifications()

  const handleConnectionError = (error: any) => {
    showError(
      'Failed to connect to Soulseek. Please check your credentials and internet connection.',
      'Connection Error',
      {
        source: 'connection',
        stack: error?.stack,
        context: { error: error?.message },
        userAction: 'Attempting to connect to Soulseek',
      }
    )
  }

  const handleDownloadError = (error: any, filename?: string) => {
    showError(
      `Failed to download ${filename || 'file'}. The user might be offline or the file unavailable.`,
      'Download Error',
      {
        source: 'download',
        stack: error?.stack,
        context: { filename, error: error?.message },
        userAction: `Attempting to download ${filename || 'file'}`,
      }
    )
  }

  const handleSearchError = (error: any, query?: string) => {
    showError(
      `Search failed for "${query || 'unknown query'}". Please try again or modify your search.`,
      'Search Error',
      {
        source: 'search',
        stack: error?.stack,
        context: { query, error: error?.message },
        userAction: `Searching for "${query || 'unknown query'}"`,
      }
    )
  }

  const handleApiError = (error: any, service?: string) => {
    showError(
      `${service || 'API'} request failed. Please check your credentials and try again.`,
      'API Error',
      {
        source: 'api',
        stack: error?.stack,
        context: { service, error: error?.message },
        userAction: `Making API request to ${service || 'unknown service'}`,
      }
    )
  }

  const handleValidationError = (message: string, field?: string) => {
    showWarning(
      message,
      'Validation Error'
    )
  }

  return {
    handleConnectionError,
    handleDownloadError,
    handleSearchError,
    handleApiError,
    handleValidationError,
  }
}
