import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  FormControlLabel,
  Switch,
  Alert,
  Chip,
  LinearProgress,
  Grid,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material'
import {
  CheckCircle as ConnectedIcon,
  Error as DisconnectedIcon,
  Warning as WarningIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
} from '@mui/icons-material'

interface ConnectionStatus {
  soulseek: 'connected' | 'connecting' | 'disconnected' | 'error'
  spotify: 'connected' | 'disconnected' | 'error'
  youtube: 'connected' | 'disconnected' | 'error'
}

interface Credentials {
  soulseek: {
    username: string
    password: string
    port: number
    useRandomCredentials: boolean
  }
  spotify: {
    clientId: string
    clientSecret: string
    accessToken: string
    refreshToken: string
  }
  youtube: {
    apiKey: string
  }
}

interface ConnectionManagerProps {
  onConnect: (service: string, credentials: any) => void
  onDisconnect: (service: string) => void
  onTestConnection: (service: string) => void
}

const ConnectionManager: React.FC<ConnectionManagerProps> = ({
  onConnect,
  onDisconnect,
  onTestConnection,
}) => {
  const [status, setStatus] = useState<ConnectionStatus>({
    soulseek: 'disconnected',
    spotify: 'disconnected',
    youtube: 'disconnected',
  })

  const [credentials, setCredentials] = useState<Credentials>({
    soulseek: {
      username: '',
      password: '',
      port: 49998,
      useRandomCredentials: false,
    },
    spotify: {
      clientId: '',
      clientSecret: '',
      accessToken: '',
      refreshToken: '',
    },
    youtube: {
      apiKey: '',
    },
  })

  const [showPasswords, setShowPasswords] = useState({
    soulseek: false,
    spotify: false,
    youtube: false,
  })

  const [helpDialogOpen, setHelpDialogOpen] = useState(false)
  const [helpService, setHelpService] = useState<string>('')

  const getStatusColor = (serviceStatus: string) => {
    switch (serviceStatus) {
      case 'connected':
        return 'success'
      case 'connecting':
        return 'warning'
      case 'error':
        return 'error'
      case 'disconnected':
      default:
        return 'default'
    }
  }

  const getStatusIcon = (serviceStatus: string) => {
    switch (serviceStatus) {
      case 'connected':
        return <ConnectedIcon color="success" />
      case 'connecting':
        return <WarningIcon color="warning" />
      case 'error':
        return <DisconnectedIcon color="error" />
      case 'disconnected':
      default:
        return <DisconnectedIcon color="disabled" />
    }
  }

  const getStatusText = (serviceStatus: string) => {
    switch (serviceStatus) {
      case 'connected':
        return 'Connected'
      case 'connecting':
        return 'Connecting...'
      case 'error':
        return 'Connection Error'
      case 'disconnected':
      default:
        return 'Disconnected'
    }
  }

  const handleConnect = (service: string) => {
    setStatus(prev => ({ ...prev, [service]: 'connecting' }))

    // Simulate connection process
    setTimeout(() => {
      const serviceCredentials = credentials[service as keyof Credentials]
      onConnect(service, serviceCredentials)
      setStatus(prev => ({ ...prev, [service]: 'connected' }))
    }, 2000)
  }

  const handleDisconnect = (service: string) => {
    setStatus(prev => ({ ...prev, [service]: 'disconnected' }))
    onDisconnect(service)
  }

  const handleTestConnection = (service: string) => {
    onTestConnection(service)
  }

  const updateCredentials = (service: string, field: string, value: any) => {
    setCredentials(prev => ({
      ...prev,
      [service]: {
        ...prev[service as keyof Credentials],
        [field]: value,
      },
    }))
  }

  const togglePasswordVisibility = (service: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [service]: !prev[service as keyof typeof prev],
    }))
  }

  const openHelp = (service: string) => {
    setHelpService(service)
    setHelpDialogOpen(true)
  }

  const getHelpContent = (service: string) => {
    switch (service) {
      case 'soulseek':
        return {
          title: 'Soulseek Connection Help',
          content: (
            <Box>
              <Typography paragraph>
                To connect to Soulseek, you need a valid username and password. You can create a free account at soulseekqt.net.
              </Typography>
              <Typography variant="h6" gutterBottom>
                Tips:
              </Typography>
              <ul>
                <li>Use your existing Soulseek account credentials</li>
                <li>The default port (49998) works for most users</li>
                <li>Enable "Use random credentials" for anonymous browsing</li>
                <li>Make sure your firewall allows the connection</li>
              </ul>
            </Box>
          ),
        }
      case 'spotify':
        return {
          title: 'Spotify API Setup Help',
          content: (
            <Box>
              <Typography paragraph>
                To download from Spotify playlists, you need to create a Spotify app and get API credentials.
              </Typography>
              <Typography variant="h6" gutterBottom>
                Steps:
              </Typography>
              <ol>
                <li>Go to developer.spotify.com/dashboard</li>
                <li>Create a new app</li>
                <li>Copy the Client ID and Client Secret</li>
                <li>Add "http://localhost:8080/callback" as a redirect URI</li>
                <li>Paste the credentials here</li>
              </ol>
            </Box>
          ),
        }
      case 'youtube':
        return {
          title: 'YouTube API Setup Help',
          content: (
            <Box>
              <Typography paragraph>
                YouTube API key is optional but improves reliability for playlist downloads.
              </Typography>
              <Typography variant="h6" gutterBottom>
                Steps:
              </Typography>
              <ol>
                <li>Go to console.cloud.google.com</li>
                <li>Create a new project or select existing</li>
                <li>Enable YouTube Data API v3</li>
                <li>Create credentials (API Key)</li>
                <li>Copy the API key here</li>
              </ol>
            </Box>
          ),
        }
      default:
        return { title: 'Help', content: <Typography>No help available</Typography> }
    }
  }

  return (
    <Box>
      {/* Connection Status Overview */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Connection Status
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getStatusIcon(status.soulseek)}
                <Box>
                  <Typography variant="body2" fontWeight="medium">
                    Soulseek
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {getStatusText(status.soulseek)}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getStatusIcon(status.spotify)}
                <Box>
                  <Typography variant="body2" fontWeight="medium">
                    Spotify API
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {getStatusText(status.spotify)}
                  </Typography>
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getStatusIcon(status.youtube)}
                <Box>
                  <Typography variant="body2" fontWeight="medium">
                    YouTube API
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {getStatusText(status.youtube)}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Soulseek Connection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Soulseek Connection
            </Typography>
            <Box>
              <Tooltip title="Help">
                <IconButton onClick={() => openHelp('soulseek')}>
                  <InfoIcon />
                </IconButton>
              </Tooltip>
              <Chip
                label={getStatusText(status.soulseek)}
                color={getStatusColor(status.soulseek)}
                icon={getStatusIcon(status.soulseek)}
                variant="outlined"
              />
            </Box>
          </Box>

          {status.soulseek === 'connecting' && (
            <LinearProgress sx={{ mb: 2 }} />
          )}

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Username"
                value={credentials.soulseek.username}
                onChange={(e) => updateCredentials('soulseek', 'username', e.target.value)}
                disabled={status.soulseek === 'connected'}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Password"
                type={showPasswords.soulseek ? 'text' : 'password'}
                value={credentials.soulseek.password}
                onChange={(e) => updateCredentials('soulseek', 'password', e.target.value)}
                disabled={status.soulseek === 'connected'}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() => togglePasswordVisibility('soulseek')}
                      edge="end"
                    >
                      {showPasswords.soulseek ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  ),
                }}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Listen Port"
                type="number"
                value={credentials.soulseek.port}
                onChange={(e) => updateCredentials('soulseek', 'port', parseInt(e.target.value))}
                disabled={status.soulseek === 'connected'}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={credentials.soulseek.useRandomCredentials}
                    onChange={(e) => updateCredentials('soulseek', 'useRandomCredentials', e.target.checked)}
                    disabled={status.soulseek === 'connected'}
                  />
                }
                label="Use random login credentials"
              />
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            {status.soulseek === 'connected' ? (
              <Button
                variant="outlined"
                color="error"
                onClick={() => handleDisconnect('soulseek')}
              >
                Disconnect
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={() => handleConnect('soulseek')}
                disabled={!credentials.soulseek.username || !credentials.soulseek.password}
              >
                Connect
              </Button>
            )}

            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => handleTestConnection('soulseek')}
            >
              Test Connection
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Spotify Connection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Spotify API Connection
            </Typography>
            <Box>
              <Tooltip title="Help">
                <IconButton onClick={() => openHelp('spotify')}>
                  <InfoIcon />
                </IconButton>
              </Tooltip>
              <Chip
                label={getStatusText(status.spotify)}
                color={getStatusColor(status.spotify)}
                icon={getStatusIcon(status.spotify)}
                variant="outlined"
              />
            </Box>
          </Box>

          {status.spotify === 'connecting' && (
            <LinearProgress sx={{ mb: 2 }} />
          )}

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Client ID"
                value={credentials.spotify.clientId}
                onChange={(e) => updateCredentials('spotify', 'clientId', e.target.value)}
                disabled={status.spotify === 'connected'}
                sx={{ mb: 2 }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Client Secret"
                type={showPasswords.spotify ? 'text' : 'password'}
                value={credentials.spotify.clientSecret}
                onChange={(e) => updateCredentials('spotify', 'clientSecret', e.target.value)}
                disabled={status.spotify === 'connected'}
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() => togglePasswordVisibility('spotify')}
                      edge="end"
                    >
                      {showPasswords.spotify ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  ),
                }}
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            {status.spotify === 'connected' ? (
              <Button
                variant="outlined"
                color="error"
                onClick={() => handleDisconnect('spotify')}
              >
                Disconnect
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={() => handleConnect('spotify')}
                disabled={!credentials.spotify.clientId || !credentials.spotify.clientSecret}
              >
                Connect
              </Button>
            )}

            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => handleTestConnection('spotify')}
            >
              Test Connection
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* YouTube Connection */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              YouTube API Connection
            </Typography>
            <Box>
              <Tooltip title="Help">
                <IconButton onClick={() => openHelp('youtube')}>
                  <InfoIcon />
                </IconButton>
              </Tooltip>
              <Chip
                label={getStatusText(status.youtube)}
                color={getStatusColor(status.youtube)}
                icon={getStatusIcon(status.youtube)}
                variant="outlined"
              />
            </Box>
          </Box>

          {status.youtube === 'connecting' && (
            <LinearProgress sx={{ mb: 2 }} />
          )}

          <TextField
            fullWidth
            label="API Key"
            type={showPasswords.youtube ? 'text' : 'password'}
            value={credentials.youtube.apiKey}
            onChange={(e) => updateCredentials('youtube', 'apiKey', e.target.value)}
            disabled={status.youtube === 'connected'}
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => togglePasswordVisibility('youtube')}
                  edge="end"
                >
                  {showPasswords.youtube ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              ),
            }}
            sx={{ mb: 2 }}
          />

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            {status.youtube === 'connected' ? (
              <Button
                variant="outlined"
                color="error"
                onClick={() => handleDisconnect('youtube')}
              >
                Disconnect
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={() => handleConnect('youtube')}
                disabled={!credentials.youtube.apiKey}
              >
                Connect
              </Button>
            )}

            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => handleTestConnection('youtube')}
            >
              Test Connection
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Help Dialog */}
      <Dialog open={helpDialogOpen} onClose={() => setHelpDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {getHelpContent(helpService).title}
        </DialogTitle>
        <DialogContent>
          {getHelpContent(helpService).content}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setHelpDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ConnectionManager
