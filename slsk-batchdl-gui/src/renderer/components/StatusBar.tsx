import React from 'react'
import {
  Box,
  Typography,
  LinearProgress,
  useTheme,
} from '@mui/material'

import ConnectionStatusIndicator from './ConnectionStatusIndicator'

interface StatusBarProps {
  activeDownloads?: number
  totalDownloads?: number
  downloadProgress?: number
  currentOperation?: string
  onOpenConnectionSettings?: () => void
}

const StatusBar: React.FC<StatusBarProps> = ({
  connectionStatus = 'disconnected',
  activeDownloads = 0,
  totalDownloads = 0,
  downloadProgress = 0,
  currentOperation = 'Ready',
}) => {
  const theme = useTheme()

  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'success'
      case 'connecting':
        return 'warning'
      case 'disconnected':
      default:
        return 'error'
    }
  }

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <ConnectedIcon fontSize="small" />
      case 'connecting':
        return <WarningIcon fontSize="small" />
      case 'disconnected':
      default:
        return <DisconnectedIcon fontSize="small" />
    }
  }

  const getConnectionText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected to Soulseek'
      case 'connecting':
        return 'Connecting...'
      case 'disconnected':
      default:
        return 'Not connected'
    }
  }

  return (
    <Box
      sx={{
        borderTop: 1,
        borderColor: 'divider',
        backgroundColor: theme.palette.background.paper,
        px: 2,
        py: 1,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        minHeight: 48,
      }}
    >
      {/* Connection Status */}
      <Chip
        icon={getConnectionIcon()}
        label={getConnectionText()}
        color={getConnectionColor()}
        variant="outlined"
        size="small"
      />

      {/* Current Operation */}
      <Typography variant="body2" color="text.secondary">
        {currentOperation}
      </Typography>

      {/* Download Progress */}
      {activeDownloads > 0 && (
        <>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 200 }}>
            <Typography variant="body2" color="text.secondary">
              {activeDownloads}/{totalDownloads} downloads
            </Typography>
            <LinearProgress
              variant="determinate"
              value={downloadProgress}
              sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
            />
            <Typography variant="body2" color="text.secondary">
              {Math.round(downloadProgress)}%
            </Typography>
          </Box>
        </>
      )}

      {/* Spacer */}
      <Box sx={{ flexGrow: 1 }} />

      {/* App Version */}
      <Typography variant="caption" color="text.disabled">
        v1.0.0
      </Typography>
    </Box>
  )
}

export default StatusBar
