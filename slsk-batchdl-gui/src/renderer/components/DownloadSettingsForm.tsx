import React from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid,
  Slider,
  Alert,
  Tooltip,
  IconButton,
} from '@mui/material'
import {
  Help as HelpIcon,
  Speed as SpeedIcon,
  Timer as TimerIcon,
  Search as SearchIcon,
  Download as DownloadIcon,
} from '@mui/icons-material'

interface DownloadSettings {
  concurrentProcesses: number
  searchTimeout: number
  maxStaleTime: number
  searchesPerTime: number
  searchRenewTime: number
  desperateSearch: boolean
  fastSearch: boolean
  artistMaybeWrong: boolean
  removeFt: boolean
  skipExisting: boolean
  writePlaylist: boolean
  writeIndex: boolean
  noProgress: boolean
}

interface DownloadSettingsFormProps {
  settings: DownloadSettings
  onChange: (settings: DownloadSettings) => void
}

const DownloadSettingsForm: React.FC<DownloadSettingsFormProps> = ({
  settings,
  onChange,
}) => {
  const handleChange = (field: keyof DownloadSettings, value: any) => {
    onChange({
      ...settings,
      [field]: value,
    })
  }

  const getSearchTimeoutText = (value: number) => {
    if (value < 1000) return `${value}ms (Very Fast)`
    if (value < 3000) return `${value}ms (Fast)`
    if (value < 6000) return `${value}ms (Normal)`
    if (value < 10000) return `${value}ms (Slow)`
    return `${value}ms (Very Slow)`
  }

  const getConcurrentProcessesText = (value: number) => {
    if (value === 1) return '1 (Conservative)'
    if (value <= 3) return `${value} (Recommended)`
    if (value <= 5) return `${value} (Aggressive)`
    return `${value} (Very Aggressive)`
  }

  return (
    <Box>
      {/* Performance Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <SpeedIcon color="primary" />
            <Typography variant="h6">
              Performance Settings
            </Typography>
            <Tooltip title="Settings that affect download speed and system resource usage">
              <IconButton size="small">
                <HelpIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Concurrent Downloads: {getConcurrentProcessesText(settings.concurrentProcesses)}
              </Typography>
              <Slider
                value={settings.concurrentProcesses}
                onChange={(_, value) => handleChange('concurrentProcesses', value as number)}
                min={1}
                max={10}
                step={1}
                marks={[
                  { value: 1, label: '1' },
                  { value: 3, label: '3' },
                  { value: 5, label: '5' },
                  { value: 10, label: '10' },
                ]}
                valueLabelDisplay="auto"
              />
              <Typography variant="caption" color="text.secondary">
                Higher values download faster but use more bandwidth and may trigger rate limits
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Search Timeout: {getSearchTimeoutText(settings.searchTimeout)}
              </Typography>
              <Slider
                value={settings.searchTimeout}
                onChange={(_, value) => handleChange('searchTimeout', value as number)}
                min={1000}
                max={15000}
                step={500}
                marks={[
                  { value: 1000, label: '1s' },
                  { value: 6000, label: '6s' },
                  { value: 10000, label: '10s' },
                  { value: 15000, label: '15s' },
                ]}
                valueLabelDisplay="auto"
              />
              <Typography variant="caption" color="text.secondary">
                How long to wait for search results before giving up
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Max Stale Time (ms)"
                value={settings.maxStaleTime}
                onChange={(e) => handleChange('maxStaleTime', parseInt(e.target.value) || 0)}
                helperText="Maximum time to wait for additional search results"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Search Renew Time (ms)"
                value={settings.searchRenewTime}
                onChange={(e) => handleChange('searchRenewTime', parseInt(e.target.value) || 0)}
                helperText="How often to refresh search results"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                type="number"
                label="Searches Per Time Period"
                value={settings.searchesPerTime}
                onChange={(e) => handleChange('searchesPerTime', parseInt(e.target.value) || 0)}
                helperText="Number of searches allowed per time period (rate limiting)"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Search Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <SearchIcon color="primary" />
            <Typography variant="h6">
              Search Settings
            </Typography>
            <Tooltip title="Settings that affect how searches are performed">
              <IconButton size="small">
                <HelpIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.fastSearch}
                    onChange={(e) => handleChange('fastSearch', e.target.checked)}
                  />
                }
                label="Fast search mode"
              />
              <Typography variant="caption" color="text.secondary" display="block">
                Use faster but less thorough search algorithm
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.desperateSearch}
                    onChange={(e) => handleChange('desperateSearch', e.target.checked)}
                  />
                }
                label="Desperate search mode"
              />
              <Typography variant="caption" color="text.secondary" display="block">
                Try alternative search strategies when normal search fails
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.artistMaybeWrong}
                    onChange={(e) => handleChange('artistMaybeWrong', e.target.checked)}
                  />
                }
                label="Artist may be wrong"
              />
              <Typography variant="caption" color="text.secondary" display="block">
                Search without artist name if initial search fails
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.removeFt}
                    onChange={(e) => handleChange('removeFt', e.target.checked)}
                  />
                }
                label="Remove 'feat.' from searches"
              />
              <Typography variant="caption" color="text.secondary" display="block">
                Strip featuring artists from search queries
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Output Settings */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <DownloadIcon color="primary" />
            <Typography variant="h6">
              Output Settings
            </Typography>
            <Tooltip title="Settings that affect how files are saved and organized">
              <IconButton size="small">
                <HelpIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.skipExisting}
                    onChange={(e) => handleChange('skipExisting', e.target.checked)}
                  />
                }
                label="Skip existing files"
              />
              <Typography variant="caption" color="text.secondary" display="block">
                Don't download files that already exist in the output directory
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.writePlaylist}
                    onChange={(e) => handleChange('writePlaylist', e.target.checked)}
                  />
                }
                label="Write playlist files"
              />
              <Typography variant="caption" color="text.secondary" display="block">
                Create M3U playlist files for downloaded tracks
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.writeIndex}
                    onChange={(e) => handleChange('writeIndex', e.target.checked)}
                  />
                }
                label="Write index files"
              />
              <Typography variant="caption" color="text.secondary" display="block">
                Create index files with download information
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.noProgress}
                    onChange={(e) => handleChange('noProgress', e.target.checked)}
                  />
                }
                label="Disable progress display"
              />
              <Typography variant="caption" color="text.secondary" display="block">
                Don't show progress bars (may improve performance)
              </Typography>
            </Grid>
          </Grid>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Tip:</strong> For best results, start with default settings and adjust based on your network speed and the availability of files you're searching for.
            </Typography>
          </Alert>
        </CardContent>
      </Card>
    </Box>
  )
}

export default DownloadSettingsForm
