import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Grid,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Collapse,
  IconButton,
  Paper,
} from '@mui/material'
import {
  ExpandLess,
  ExpandMore,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Speed as SpeedIcon,
  Timer as TimerIcon,
  CloudDownload as DownloadIcon,
} from '@mui/icons-material'

interface ProgressStats {
  totalTracks: number
  completedTracks: number
  failedTracks: number
  activeDownloads: number
  overallProgress: number
  averageSpeed: number
  estimatedTimeRemaining: number
  totalBytesDownloaded: number
  totalBytesExpected: number
}

interface ProgressEvent {
  id: string
  timestamp: Date
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
  details?: string
}

interface ProgressMonitorProps {
  stats: ProgressStats
  events: ProgressEvent[]
  isActive: boolean
}

const ProgressMonitor: React.FC<ProgressMonitorProps> = ({
  stats,
  events,
  isActive,
}) => {
  const [expandedEvents, setExpandedEvents] = useState(false)
  const [recentEvents, setRecentEvents] = useState<ProgressEvent[]>([])

  useEffect(() => {
    // Keep only the last 50 events for performance
    setRecentEvents(events.slice(-50).reverse())
  }, [events])

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatSpeed = (bytesPerSecond: number) => {
    return formatFileSize(bytesPerSecond) + '/s'
  }

  const formatTime = (seconds: number) => {
    if (seconds === 0 || !isFinite(seconds)) return 'Unknown'
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    }
    if (minutes > 0) {
      return `${minutes}m ${secs}s`
    }
    return `${secs}s`
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <SuccessIcon color="success" fontSize="small" />
      case 'error':
        return <ErrorIcon color="error" fontSize="small" />
      case 'warning':
        return <WarningIcon color="warning" fontSize="small" />
      case 'info':
      default:
        return <InfoIcon color="info" fontSize="small" />
    }
  }

  const getEventColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'success'
      case 'error':
        return 'error'
      case 'warning':
        return 'warning'
      case 'info':
      default:
        return 'info'
    }
  }

  return (
    <Box>
      {/* Overall Progress */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Overall Progress
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                {stats.completedTracks} of {stats.totalTracks} tracks completed
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {Math.round(stats.overallProgress)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={stats.overallProgress}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <SpeedIcon color="primary" sx={{ mb: 1 }} />
                <Typography variant="h6">
                  {formatSpeed(stats.averageSpeed)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Average Speed
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <TimerIcon color="primary" sx={{ mb: 1 }} />
                <Typography variant="h6">
                  {formatTime(stats.estimatedTimeRemaining)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Time Remaining
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <DownloadIcon color="primary" sx={{ mb: 1 }} />
                <Typography variant="h6">
                  {stats.activeDownloads}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Active Downloads
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={6} md={3}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h6" color="text.primary" sx={{ mb: 1 }}>
                  {formatFileSize(stats.totalBytesDownloaded)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Downloaded
                </Typography>
              </Paper>
            </Grid>
          </Grid>

          {stats.failedTracks > 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              {stats.failedTracks} track{stats.failedTracks > 1 ? 's' : ''} failed to download. 
              Check the events below for details.
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Status Indicators */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Status
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip
              icon={<SuccessIcon />}
              label={`${stats.completedTracks} Completed`}
              color="success"
              variant="outlined"
            />
            
            {stats.failedTracks > 0 && (
              <Chip
                icon={<ErrorIcon />}
                label={`${stats.failedTracks} Failed`}
                color="error"
                variant="outlined"
              />
            )}
            
            {stats.activeDownloads > 0 && (
              <Chip
                icon={<DownloadIcon />}
                label={`${stats.activeDownloads} Active`}
                color="primary"
                variant="outlined"
              />
            )}
            
            <Chip
              label={isActive ? 'Running' : 'Stopped'}
              color={isActive ? 'success' : 'default'}
              variant={isActive ? 'filled' : 'outlined'}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Recent Events */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              Recent Events ({recentEvents.length})
            </Typography>
            <IconButton onClick={() => setExpandedEvents(!expandedEvents)}>
              {expandedEvents ? <ExpandLess /> : <ExpandMore />}
            </IconButton>
          </Box>
          
          <Collapse in={expandedEvents}>
            {recentEvents.length === 0 ? (
              <Alert severity="info" sx={{ mt: 2 }}>
                No events yet. Events will appear here as downloads progress.
              </Alert>
            ) : (
              <List sx={{ maxHeight: 400, overflow: 'auto', mt: 1 }}>
                {recentEvents.map((event) => (
                  <ListItem key={event.id} divider>
                    <ListItemIcon>
                      {getEventIcon(event.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={event.message}
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            {event.timestamp.toLocaleTimeString()}
                          </Typography>
                          {event.details && (
                            <Typography variant="caption" display="block" color="text.secondary">
                              {event.details}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Collapse>
        </CardContent>
      </Card>
    </Box>
  )
}

export default ProgressMonitor
