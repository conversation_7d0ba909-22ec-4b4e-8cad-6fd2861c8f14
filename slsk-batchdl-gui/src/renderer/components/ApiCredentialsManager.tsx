import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Grid,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  <PERSON>,
  Chip,
} from '@mui/material'
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as ValidIcon,
  Error as InvalidIcon,
  Warning as WarningIcon,
  OpenInNew as ExternalLinkIcon,
} from '@mui/icons-material'

interface ApiCredentials {
  spotify: {
    clientId: string
    clientSecret: string
    accessToken: string
    refreshToken: string
    isValid: boolean
    lastValidated: Date | null
  }
  youtube: {
    apiKey: string
    isValid: boolean
    lastValidated: Date | null
  }
}

interface ApiCredentialsManagerProps {
  credentials: ApiCredentials
  onCredentialsChange: (service: string, field: string, value: any) => void
  onValidateCredentials: (service: string) => void
  onAuthorizeSpotify: () => void
}

const ApiCredentialsManager: React.FC<ApiCredentialsManagerProps> = ({
  credentials,
  onCredentialsChange,
  onValidateCredentials,
  onAuthorizeSpotify,
}) => {
  const [showSecrets, setShowSecrets] = useState({
    spotifySecret: false,
    spotifyTokens: false,
    youtubeKey: false,
  })

  const toggleSecretVisibility = (field: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [field]: !prev[field as keyof typeof prev],
    }))
  }

  const getValidationStatus = (service: string) => {
    const creds = credentials[service as keyof ApiCredentials]
    if (!creds.isValid && creds.lastValidated) {
      return { icon: <InvalidIcon color="error" />, text: 'Invalid', color: 'error' as const }
    } else if (creds.isValid) {
      return { icon: <ValidIcon color="success" />, text: 'Valid', color: 'success' as const }
    } else {
      return { icon: <WarningIcon color="warning" />, text: 'Not validated', color: 'warning' as const }
    }
  }

  return (
    <Box>
      {/* Spotify API Credentials */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Spotify API Credentials
            </Typography>
            <Chip
              icon={getValidationStatus('spotify').icon}
              label={getValidationStatus('spotify').text}
              color={getValidationStatus('spotify').color}
              variant="outlined"
            />
          </Box>

          <Alert severity="info" sx={{ mb: 2 }}>
            Required for downloading from Spotify playlists, albums, and liked songs. 
            <Link 
              href="https://developer.spotify.com/dashboard" 
              target="_blank" 
              rel="noopener"
              sx={{ ml: 1 }}
            >
              Get credentials here <ExternalLinkIcon fontSize="small" />
            </Link>
          </Alert>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Client ID"
                value={credentials.spotify.clientId}
                onChange={(e) => onCredentialsChange('spotify', 'clientId', e.target.value)}
                placeholder="Your Spotify app client ID"
                sx={{ mb: 2 }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Client Secret"
                type={showSecrets.spotifySecret ? 'text' : 'password'}
                value={credentials.spotify.clientSecret}
                onChange={(e) => onCredentialsChange('spotify', 'clientSecret', e.target.value)}
                placeholder="Your Spotify app client secret"
                InputProps={{
                  endAdornment: (
                    <IconButton
                      onClick={() => toggleSecretVisibility('spotifySecret')}
                      edge="end"
                    >
                      {showSecrets.spotifySecret ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  ),
                }}
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>

          {/* Advanced Spotify Settings */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Advanced Settings (Optional)</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Alert severity="warning" sx={{ mb: 2 }}>
                These tokens are automatically generated during authorization. Only fill these if you have pre-generated tokens.
              </Alert>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Access Token"
                    type={showSecrets.spotifyTokens ? 'text' : 'password'}
                    value={credentials.spotify.accessToken}
                    onChange={(e) => onCredentialsChange('spotify', 'accessToken', e.target.value)}
                    placeholder="Pre-generated access token (optional)"
                    InputProps={{
                      endAdornment: (
                        <IconButton
                          onClick={() => toggleSecretVisibility('spotifyTokens')}
                          edge="end"
                        >
                          {showSecrets.spotifyTokens ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      ),
                    }}
                    sx={{ mb: 2 }}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Refresh Token"
                    type={showSecrets.spotifyTokens ? 'text' : 'password'}
                    value={credentials.spotify.refreshToken}
                    onChange={(e) => onCredentialsChange('spotify', 'refreshToken', e.target.value)}
                    placeholder="Pre-generated refresh token (optional)"
                    sx={{ mb: 2 }}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <Button
              variant="contained"
              onClick={onAuthorizeSpotify}
              disabled={!credentials.spotify.clientId || !credentials.spotify.clientSecret}
            >
              Authorize with Spotify
            </Button>
            
            <Button
              variant="outlined"
              onClick={() => onValidateCredentials('spotify')}
              disabled={!credentials.spotify.clientId || !credentials.spotify.clientSecret}
            >
              Validate Credentials
            </Button>
          </Box>

          {credentials.spotify.lastValidated && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              Last validated: {credentials.spotify.lastValidated.toLocaleString()}
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* YouTube API Credentials */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              YouTube API Credentials
            </Typography>
            <Chip
              icon={getValidationStatus('youtube').icon}
              label={getValidationStatus('youtube').text}
              color={getValidationStatus('youtube').color}
              variant="outlined"
            />
          </Box>

          <Alert severity="info" sx={{ mb: 2 }}>
            Optional but improves reliability for YouTube playlist downloads. 
            <Link 
              href="https://console.cloud.google.com" 
              target="_blank" 
              rel="noopener"
              sx={{ ml: 1 }}
            >
              Get API key here <ExternalLinkIcon fontSize="small" />
            </Link>
          </Alert>

          <TextField
            fullWidth
            label="YouTube Data API v3 Key"
            type={showSecrets.youtubeKey ? 'text' : 'password'}
            value={credentials.youtube.apiKey}
            onChange={(e) => onCredentialsChange('youtube', 'apiKey', e.target.value)}
            placeholder="Your YouTube Data API v3 key"
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => toggleSecretVisibility('youtubeKey')}
                  edge="end"
                >
                  {showSecrets.youtubeKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              ),
            }}
            sx={{ mb: 2 }}
          />

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              onClick={() => onValidateCredentials('youtube')}
              disabled={!credentials.youtube.apiKey}
            >
              Validate API Key
            </Button>
          </Box>

          {credentials.youtube.lastValidated && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              Last validated: {credentials.youtube.lastValidated.toLocaleString()}
            </Typography>
          )}

          {/* Setup Instructions */}
          <Accordion sx={{ mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Setup Instructions</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="h6" gutterBottom>
                How to get a YouTube API key:
              </Typography>
              <ol>
                <li>Go to the Google Cloud Console</li>
                <li>Create a new project or select an existing one</li>
                <li>Enable the YouTube Data API v3</li>
                <li>Go to "Credentials" and create an API key</li>
                <li>Optionally restrict the key to YouTube Data API v3</li>
                <li>Copy the API key and paste it above</li>
              </ol>
              
              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Important:</strong> Keep your API key secure and don't share it publicly. 
                  The YouTube API has usage quotas, so monitor your usage in the Google Cloud Console.
                </Typography>
              </Alert>
            </AccordionDetails>
          </Accordion>
        </CardContent>
      </Card>
    </Box>
  )
}

export default ApiCredentialsManager
