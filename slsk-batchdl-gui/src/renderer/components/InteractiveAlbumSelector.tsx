import React, { useState, useEffect, useRef } from 'react'
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  TextField,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormControlLabel,
  Alert,
  LinearProgress,
  Divider,
  Grid,
  Paper,
} from '@mui/material'
import {
  Folder as FolderIcon,
  AudioFile as AudioFileIcon,
  Download as DownloadIcon,
  FilterList as FilterIcon,
  NavigateNext as NextIcon,
  NavigateBefore as PrevIcon,
  Refresh as RefreshIcon,
  Help as HelpIcon,
  Close as CloseIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  PlayArrow as PlayIcon,
  FolderOpen as FolderOpenIcon,
} from '@mui/icons-material'

interface Track {
  id: string
  filename: string
  title: string
  artist: string
  album: string
  duration: number
  bitrate: number
  fileSize: number
  username: string
  selected: boolean
}

interface AlbumFolder {
  id: string
  path: string
  username: string
  tracks: Track[]
  totalSize: number
  averageBitrate: number
}

interface InteractiveAlbumSelectorProps {
  folders: AlbumFolder[]
  onDownload: (folderId: string, selectedTrackIds: string[]) => void
  onClose: () => void
  onRefresh: () => void
  isLoading?: boolean
}

const InteractiveAlbumSelector: React.FC<InteractiveAlbumSelectorProps> = ({
  folders,
  onDownload,
  onClose,
  onRefresh,
  isLoading = false,
}) => {
  const [currentFolderIndex, setCurrentFolderIndex] = useState(0)
  const [selectedTracks, setSelectedTracks] = useState<Set<string>>(new Set())
  const [filterQuery, setFilterQuery] = useState('')
  const [filteredFolders, setFilteredFolders] = useState<AlbumFolder[]>(folders)
  const [showHelp, setShowHelp] = useState(false)
  const [selectAll, setSelectAll] = useState(false)
  const listRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (filterQuery.trim()) {
      const filtered = folders.filter(folder =>
        folder.tracks.some(track =>
          track.filename.toLowerCase().includes(filterQuery.toLowerCase()) ||
          track.title.toLowerCase().includes(filterQuery.toLowerCase()) ||
          track.artist.toLowerCase().includes(filterQuery.toLowerCase()) ||
          track.album.toLowerCase().includes(filterQuery.toLowerCase())
        )
      )
      setFilteredFolders(filtered)
      setCurrentFolderIndex(0)
    } else {
      setFilteredFolders(folders)
    }
  }, [filterQuery, folders])

  useEffect(() => {
    // Reset selections when folder changes
    setSelectedTracks(new Set())
    setSelectAll(false)
  }, [currentFolderIndex])

  const currentFolder = filteredFolders[currentFolderIndex]

  const handlePrevFolder = () => {
    setCurrentFolderIndex(prev => 
      prev > 0 ? prev - 1 : filteredFolders.length - 1
    )
  }

  const handleNextFolder = () => {
    setCurrentFolderIndex(prev => 
      prev < filteredFolders.length - 1 ? prev + 1 : 0
    )
  }

  const handleTrackSelection = (trackId: string) => {
    setSelectedTracks(prev => {
      const newSet = new Set(prev)
      if (newSet.has(trackId)) {
        newSet.delete(trackId)
      } else {
        newSet.add(trackId)
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedTracks(new Set())
    } else {
      setSelectedTracks(new Set(currentFolder?.tracks.map(t => t.id) || []))
    }
    setSelectAll(!selectAll)
  }

  const handleDownloadSelected = () => {
    if (currentFolder && selectedTracks.size > 0) {
      onDownload(currentFolder.id, Array.from(selectedTracks))
    }
  }

  const handleDownloadAll = () => {
    if (currentFolder) {
      onDownload(currentFolder.id, currentFolder.tracks.map(t => t.id))
    }
  }

  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <LinearProgress sx={{ mb: 2 }} />
            <Typography>Loading album folders...</Typography>
          </Box>
        </CardContent>
      </Card>
    )
  }

  if (!currentFolder) {
    return (
      <Card>
        <CardContent>
          <Alert severity="info">
            No album folders found. Try adjusting your search criteria.
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">
              Interactive Album Selection
            </Typography>
            <Box>
              <Tooltip title="Help">
                <IconButton onClick={() => setShowHelp(true)}>
                  <HelpIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh">
                <IconButton onClick={onRefresh}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Close">
                <IconButton onClick={onClose}>
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Filter */}
          <TextField
            fullWidth
            size="small"
            placeholder="Filter folders by filename, title, artist, or album..."
            value={filterQuery}
            onChange={(e) => setFilterQuery(e.target.value)}
            InputProps={{
              startAdornment: <FilterIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
            sx={{ mb: 2 }}
          />

          {/* Navigation */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton onClick={handlePrevFolder} disabled={filteredFolders.length <= 1}>
                <PrevIcon />
              </IconButton>
              <Typography variant="body2">
                {currentFolderIndex + 1} of {filteredFolders.length}
              </Typography>
              <IconButton onClick={handleNextFolder} disabled={filteredFolders.length <= 1}>
                <NextIcon />
              </IconButton>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Chip
                icon={<FolderOpenIcon />}
                label={`${currentFolder.tracks.length} tracks`}
                size="small"
              />
              <Chip
                label={formatFileSize(currentFolder.totalSize)}
                size="small"
              />
              <Chip
                label={`${currentFolder.averageBitrate} kbps avg`}
                size="small"
              />
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Folder Info */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <FolderIcon color="primary" />
            <Typography variant="h6" noWrap>
              {currentFolder.path}
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            Shared by: {currentFolder.username}
          </Typography>
        </CardContent>
      </Card>

      {/* Track List */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectAll}
                  onChange={handleSelectAll}
                  indeterminate={selectedTracks.size > 0 && selectedTracks.size < currentFolder.tracks.length}
                />
              }
              label={`Select All (${selectedTracks.size}/${currentFolder.tracks.length})`}
            />

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={handleDownloadSelected}
                disabled={selectedTracks.size === 0}
              >
                Download Selected
              </Button>
              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                onClick={handleDownloadAll}
              >
                Download All
              </Button>
            </Box>
          </Box>

          <List ref={listRef} sx={{ maxHeight: 400, overflow: 'auto' }}>
            {currentFolder.tracks.map((track, index) => (
              <ListItem
                key={track.id}
                sx={{
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 1,
                  mb: 1,
                  '&:hover': { bgcolor: 'action.hover' },
                }}
              >
                <ListItemIcon>
                  <Checkbox
                    checked={selectedTracks.has(track.id)}
                    onChange={() => handleTrackSelection(track.id)}
                  />
                </ListItemIcon>
                <ListItemIcon>
                  <AudioFileIcon />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {index + 1}. {track.title || track.filename}
                      </Typography>
                      {track.artist && (
                        <Typography variant="body2" color="text.secondary">
                          by {track.artist}
                        </Typography>
                      )}
                    </Box>
                  }
                  secondary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 0.5 }}>
                      <Typography variant="caption">
                        {formatDuration(track.duration)}
                      </Typography>
                      <Typography variant="caption">
                        {track.bitrate} kbps
                      </Typography>
                      <Typography variant="caption">
                        {formatFileSize(track.fileSize)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {track.filename}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>

      {/* Help Dialog */}
      <Dialog open={showHelp} onClose={() => setShowHelp(false)} maxWidth="md" fullWidth>
        <DialogTitle>Interactive Album Selection Help</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            This interface allows you to browse through different album folders found for your search
            and select specific tracks to download.
          </Typography>
          
          <Typography variant="h6" gutterBottom>
            Features:
          </Typography>
          <ul>
            <li>Navigate between folders using the arrow buttons</li>
            <li>Filter folders by searching for specific content</li>
            <li>Select individual tracks or all tracks in a folder</li>
            <li>View detailed information about each track</li>
            <li>Download selected tracks or entire folders</li>
          </ul>

          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Track Information:
          </Typography>
          <ul>
            <li><strong>Duration:</strong> Length of the audio file</li>
            <li><strong>Bitrate:</strong> Audio quality (higher = better quality)</li>
            <li><strong>File Size:</strong> Size of the file to download</li>
            <li><strong>Filename:</strong> Original filename on the user's computer</li>
          </ul>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowHelp(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default InteractiveAlbumSelector
