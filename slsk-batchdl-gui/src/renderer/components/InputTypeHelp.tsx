import React, { useState } from 'react'
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  <PERSON>alogContent,
  DialogActions,
  Button,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tabs,
  Tab,
  Box,
  Chip,
  Alert,
} from '@mui/material'
import {
  Info as InfoIcon,
  MusicNote as MusicIcon,
  VideoLibrary as VideoIcon,
  TableChart as CsvIcon,
  Album as AlbumIcon,
  Search as SearchIcon,
} from '@mui/icons-material'

import { InputType, DownloadMode } from '../types/slsk'
import { getInputTypeHints, getDownloadModeHints } from '../utils/validation'

interface InputTypeHelpProps {
  open: boolean
  onClose: () => void
  inputType?: InputType
  downloadMode?: DownloadMode
}

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`help-tabpanel-${index}`}
      aria-labelledby={`help-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

const InputTypeHelp: React.FC<InputTypeHelpProps> = ({
  open,
  onClose,
  inputType = 'string',
  downloadMode = 'normal',
}) => {
  const [tabValue, setTabValue] = useState(0)

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  const getInputTypeIcon = (type: InputType) => {
    switch (type) {
      case 'spotify':
        return <MusicIcon color="success" />
      case 'youtube':
        return <VideoIcon color="error" />
      case 'csv':
        return <CsvIcon color="info" />
      case 'bandcamp':
        return <AlbumIcon color="warning" />
      case 'string':
      default:
        return <SearchIcon color="primary" />
    }
  }

  const inputTypes: { type: InputType; label: string; description: string }[] = [
    {
      type: 'string',
      label: 'Search String',
      description: 'Search for tracks using artist and song names',
    },
    {
      type: 'spotify',
      label: 'Spotify',
      description: 'Download from Spotify playlists, albums, or liked songs',
    },
    {
      type: 'youtube',
      label: 'YouTube',
      description: 'Download from YouTube playlists',
    },
    {
      type: 'csv',
      label: 'CSV File',
      description: 'Download from CSV file with track information',
    },
    {
      type: 'bandcamp',
      label: 'Bandcamp',
      description: 'Download from Bandcamp albums or artists',
    },
  ]

  const downloadModes: { mode: DownloadMode; label: string; description: string }[] = [
    {
      mode: 'normal',
      label: 'Normal',
      description: 'Download individual tracks',
    },
    {
      mode: 'album',
      label: 'Album',
      description: 'Download entire albums/folders',
    },
    {
      mode: 'aggregate',
      label: 'Aggregate',
      description: 'Find all distinct songs by an artist',
    },
    {
      mode: 'album-aggregate',
      label: 'Album Aggregate',
      description: 'Find all distinct albums by an artist',
    },
  ]

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Input Types & Download Modes Help
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Input Types" />
            <Tab label="Download Modes" />
            <Tab label="Examples" />
          </Tabs>
        </Box>

        {/* Input Types Tab */}
        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            Available Input Types
          </Typography>
          
          <List>
            {inputTypes.map((type) => (
              <ListItem key={type.type} sx={{ mb: 2 }}>
                <ListItemIcon>
                  {getInputTypeIcon(type.type)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle1" fontWeight="medium">
                        {type.label}
                      </Typography>
                      {type.type === inputType && (
                        <Chip label="Current" size="small" color="primary" />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {type.description}
                      </Typography>
                      <List dense>
                        {getInputTypeHints(type.type).map((hint, index) => (
                          <ListItem key={index} sx={{ pl: 0 }}>
                            <ListItemIcon sx={{ minWidth: 20 }}>
                              <InfoIcon fontSize="small" color="action" />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Typography variant="caption" color="text.secondary">
                                  {hint}
                                </Typography>
                              }
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </TabPanel>

        {/* Download Modes Tab */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Download Modes
          </Typography>
          
          <List>
            {downloadModes.map((mode) => (
              <ListItem key={mode.mode} sx={{ mb: 2 }}>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle1" fontWeight="medium">
                        {mode.label}
                      </Typography>
                      {mode.mode === downloadMode && (
                        <Chip label="Current" size="small" color="primary" />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {mode.description}
                      </Typography>
                      <List dense>
                        {getDownloadModeHints(mode.mode).map((hint, index) => (
                          <ListItem key={index} sx={{ pl: 0 }}>
                            <ListItemIcon sx={{ minWidth: 20 }}>
                              <InfoIcon fontSize="small" color="action" />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Typography variant="caption" color="text.secondary">
                                  {hint}
                                </Typography>
                              }
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </TabPanel>

        {/* Examples Tab */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Usage Examples
          </Typography>

          <Alert severity="info" sx={{ mb: 2 }}>
            Here are some common usage patterns to get you started
          </Alert>

          <Typography variant="subtitle2" gutterBottom>
            Spotify Examples:
          </Typography>
          <List dense sx={{ mb: 2 }}>
            <ListItem>
              <ListItemText primary="spotify-likes" secondary="Download your liked songs" />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M" 
                secondary="Download a public playlist" 
              />
            </ListItem>
          </List>

          <Typography variant="subtitle2" gutterBottom>
            YouTube Examples:
          </Typography>
          <List dense sx={{ mb: 2 }}>
            <ListItem>
              <ListItemText 
                primary="https://youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMt9xaJGA6H_VjlXEL" 
                secondary="Download a YouTube playlist" 
              />
            </ListItem>
          </List>

          <Typography variant="subtitle2" gutterBottom>
            Search String Examples:
          </Typography>
          <List dense sx={{ mb: 2 }}>
            <ListItem>
              <ListItemText primary="Radiohead - Creep" secondary="Search for a specific song" />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="title=Bohemian Rhapsody, artist=Queen, length=355" 
                secondary="Search with specific properties" 
              />
            </ListItem>
          </List>

          <Typography variant="subtitle2" gutterBottom>
            Bandcamp Examples:
          </Typography>
          <List dense>
            <ListItem>
              <ListItemText 
                primary="https://artist.bandcamp.com/album/album-name" 
                secondary="Download a specific album" 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="https://artist.bandcamp.com" 
                secondary="Download entire artist discography" 
              />
            </ListItem>
          </List>
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Got it
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default InputTypeHelp
