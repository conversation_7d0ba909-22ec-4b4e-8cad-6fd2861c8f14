import React, { useState } from 'react'
import {
  <PERSON>,
  Drawer,
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Typo<PERSON>,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  useTheme,
  Badge,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Home as HomeIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Description as LogsIcon,
  MusicNote as MusicNoteIcon,
  PlayArrow as DemoIcon,
} from '@mui/icons-material'
import { useNavigate, useLocation } from 'react-router-dom'

import StatusBar from './StatusBar'
import ConnectionStatusIndicator from './ConnectionStatusIndicator'

const drawerWidth = 240

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const theme = useTheme()
  const navigate = useNavigate()
  const location = useLocation()

  // Connection status state
  const [connectionStatus] = useState({
    soulseek: {
      status: 'disconnected' as const,
      username: undefined,
      connectedSince: undefined,
      lastError: undefined,
    },
    spotify: {
      status: 'disconnected' as const,
      lastValidated: undefined,
      hasCredentials: false,
    },
    youtube: {
      status: 'disconnected' as const,
      lastValidated: undefined,
      hasCredentials: false,
    },
  })

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleOpenSettings = () => {
    navigate('/settings')
    setMobileOpen(false)
  }

  const menuItems = [
    { text: 'Home', icon: <HomeIcon />, path: '/' },
    { text: 'Downloads', icon: <DownloadIcon />, path: '/downloads', badge: 0 }, // Badge will be dynamic
    { text: 'Interactive Demo', icon: <DemoIcon />, path: '/demo' },
    { text: 'Settings', icon: <SettingsIcon />, path: '/settings' },
    { text: 'Logs', icon: <LogsIcon />, path: '/logs' },
  ]

  const drawer = (
    <Box>
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <MusicNoteIcon color="primary" />
          <Typography variant="h6" noWrap component="div">
            SLSK BatchDL
          </Typography>
        </Box>
      </Toolbar>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => {
                navigate(item.path)
                setMobileOpen(false)
              }}
              sx={{
                '&.Mui-selected': {
                  backgroundColor: theme.palette.primary.main + '20',
                  '&:hover': {
                    backgroundColor: theme.palette.primary.main + '30',
                  },
                },
              }}
            >
              <ListItemIcon>
                {item.badge !== undefined && item.badge > 0 ? (
                  <Badge badgeContent={item.badge} color="error">
                    {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  )

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          zIndex: theme.zIndex.drawer + 1,
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {menuItems.find(item => item.path === location.pathname)?.text || 'SLSK BatchDL'}
          </Typography>
          <ConnectionStatusIndicator
            status={connectionStatus}
            onOpenSettings={handleOpenSettings}
          />
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="navigation menu"
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',
        }}
      >
        <Toolbar /> {/* Spacer for AppBar */}

        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 3 }}>
          {children}
        </Box>

        <StatusBar />
      </Box>
    </Box>
  )
}

export default Layout
