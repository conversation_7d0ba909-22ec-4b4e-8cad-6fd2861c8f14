import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Alert,
  Chip,
  FormControlLabel,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Tooltip,
  IconButton,
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  Help as HelpIcon,
  FolderOpen as FolderIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Visibility as PreviewIcon,
} from '@mui/icons-material'

import { InputType, DownloadMode } from '../types/slsk'
import { validateInput, validateOutputDirectory, validateNumericInput } from '../utils/validation'
import InputTypeHelp from './InputTypeHelp'

interface InputConfigFormProps {
  onStart: (config: any) => void
  onPreview: (config: any) => void
  isRunning: boolean
  onStop: () => void
}

const InputConfigForm: React.FC<InputConfigFormProps> = ({
  onStart,
  onPreview,
  isRunning,
  onStop,
}) => {
  const [input, setInput] = useState('')
  const [inputType, setInputType] = useState<InputType>('string')
  const [outputDir, setOutputDir] = useState('')
  const [downloadMode, setDownloadMode] = useState<DownloadMode>('normal')
  const [maxTracks, setMaxTracks] = useState(100)
  const [offset, setOffset] = useState(0)
  const [reverse, setReverse] = useState(false)
  const [fastSearch, setFastSearch] = useState(false)
  const [skipExisting, setSkipExisting] = useState(true)
  const [helpOpen, setHelpOpen] = useState(false)

  // Validation state
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleSelectOutputDir = async () => {
    try {
      const dir = await window.electronAPI.selectDirectory()
      if (dir) {
        setOutputDir(dir)
        setErrors(prev => ({ ...prev, outputDir: '' }))
      }
    } catch (error) {
      console.error('Error selecting directory:', error)
    }
  }

  const handleSelectCsvFile = async () => {
    try {
      const file = await window.electronAPI.selectFile([
        { name: 'CSV Files', extensions: ['csv'] },
        { name: 'All Files', extensions: ['*'] },
      ])
      if (file) {
        setInput(file)
        setErrors(prev => ({ ...prev, input: '' }))
      }
    } catch (error) {
      console.error('Error selecting file:', error)
    }
  }

  const validateForm = () => {
    const inputValidation = validateInput(input, inputType)
    const outputValidation = validateOutputDirectory(outputDir)
    const maxTracksValidation = validateNumericInput(maxTracks, 'maxTracks', 1)
    const offsetValidation = validateNumericInput(offset, 'offset', 0)

    const allErrors = {
      ...inputValidation.errors,
      ...outputValidation.errors,
      ...maxTracksValidation.errors,
      ...offsetValidation.errors,
    }

    setErrors(allErrors)
    return Object.keys(allErrors).length === 0
  }

  const handleStart = () => {
    if (validateForm()) {
      const config = {
        input,
        inputType,
        outputDir,
        downloadMode,
        maxTracks,
        offset,
        reverse,
        fastSearch,
        skipExisting,
      }
      onStart(config)
    }
  }

  const handlePreview = () => {
    if (validateForm()) {
      const config = {
        input,
        inputType,
        outputDir,
        downloadMode,
        maxTracks,
        offset,
        reverse,
        fastSearch,
        skipExisting,
      }
      onPreview(config)
    }
  }

  const getInputPlaceholder = () => {
    switch (inputType) {
      case 'spotify':
        return 'https://open.spotify.com/playlist/... or "spotify-likes"'
      case 'youtube':
        return 'https://youtube.com/playlist?list=...'
      case 'csv':
        return 'Click "Browse" to select a CSV file'
      case 'bandcamp':
        return 'https://artist.bandcamp.com/album/...'
      case 'string':
      default:
        return 'Artist - Song Title or search query'
    }
  }

  const getInputTypeDescription = () => {
    switch (inputType) {
      case 'spotify':
        return 'Download from Spotify playlists, albums, or liked songs. Requires API credentials for private playlists.'
      case 'youtube':
        return 'Download from YouTube playlists. Optionally use YouTube API for better reliability.'
      case 'csv':
        return 'Download from CSV file with track information. Supports Artist, Title, Album, Length columns.'
      case 'bandcamp':
        return 'Download from Bandcamp albums, tracks, or entire artist discographies.'
      case 'string':
      default:
        return 'Search for individual tracks or albums using artist and song names.'
    }
  }

  const getDownloadModeDescription = () => {
    switch (downloadMode) {
      case 'album':
        return 'Download entire albums/folders from Soulseek'
      case 'interactive':
        return 'Browse and select specific tracks from album folders interactively'
      case 'aggregate':
        return 'Find and download all distinct songs by an artist'
      case 'album-aggregate':
        return 'Find and download all distinct albums by an artist'
      case 'normal':
      default:
        return 'Download individual tracks'
    }
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Download Configuration
        </Typography>

        {/* Input Type Selection */}
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end', mb: 2 }}>
          <FormControl fullWidth>
            <InputLabel>Input Type</InputLabel>
            <Select
              value={inputType}
              label="Input Type"
              onChange={(e) => {
                setInputType(e.target.value as InputType)
                setInput('')
                setErrors(prev => ({ ...prev, input: '' }))
              }}
            >
              <MenuItem value="string">Search String</MenuItem>
              <MenuItem value="spotify">Spotify</MenuItem>
              <MenuItem value="youtube">YouTube</MenuItem>
              <MenuItem value="csv">CSV File</MenuItem>
              <MenuItem value="bandcamp">Bandcamp</MenuItem>
            </Select>
          </FormControl>
          <Tooltip title="Show help for input types and download modes">
            <IconButton onClick={() => setHelpOpen(true)} color="primary">
              <HelpIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Input Type Description */}
        <Alert severity="info" sx={{ mb: 2 }}>
          {getInputTypeDescription()}
        </Alert>

        {/* Input Field */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            label="Input"
            placeholder={getInputPlaceholder()}
            value={input}
            onChange={(e) => {
              setInput(e.target.value)
              setErrors(prev => ({ ...prev, input: '' }))
            }}
            error={!!errors.input}
            helperText={errors.input}
            multiline={inputType === 'string'}
            rows={inputType === 'string' ? 2 : 1}
            disabled={inputType === 'csv'}
          />
          {inputType === 'csv' && (
            <Button
              variant="outlined"
              onClick={handleSelectCsvFile}
              sx={{ minWidth: 100 }}
            >
              Browse
            </Button>
          )}
        </Box>

        {/* Output Directory */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            label="Output Directory"
            value={outputDir}
            onChange={(e) => {
              setOutputDir(e.target.value)
              setErrors(prev => ({ ...prev, outputDir: '' }))
            }}
            error={!!errors.outputDir}
            helperText={errors.outputDir}
            placeholder="Select download directory..."
          />
          <Button
            variant="outlined"
            startIcon={<FolderIcon />}
            onClick={handleSelectOutputDir}
          >
            Browse
          </Button>
        </Box>

        {/* Advanced Options */}
        <Accordion sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Advanced Options</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Download Mode</InputLabel>
                  <Select
                    value={downloadMode}
                    label="Download Mode"
                    onChange={(e) => setDownloadMode(e.target.value as DownloadMode)}
                  >
                    <MenuItem value="normal">Normal</MenuItem>
                    <MenuItem value="album">Album</MenuItem>
                    <MenuItem value="interactive">Interactive Album</MenuItem>
                    <MenuItem value="aggregate">Aggregate</MenuItem>
                    <MenuItem value="album-aggregate">Album Aggregate</MenuItem>
                  </Select>
                </FormControl>
                <Typography variant="caption" color="text.secondary">
                  {getDownloadModeDescription()}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="number"
                  label="Max Tracks"
                  value={maxTracks}
                  onChange={(e) => setMaxTracks(parseInt(e.target.value) || 0)}
                  error={!!errors.maxTracks}
                  helperText={errors.maxTracks}
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  type="number"
                  label="Offset"
                  value={offset}
                  onChange={(e) => setOffset(parseInt(e.target.value) || 0)}
                  error={!!errors.offset}
                  helperText={errors.offset}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={reverse}
                      onChange={(e) => setReverse(e.target.checked)}
                    />
                  }
                  label="Download in reverse order"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={fastSearch}
                      onChange={(e) => setFastSearch(e.target.checked)}
                    />
                  }
                  label="Fast search mode"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={skipExisting}
                      onChange={(e) => setSkipExisting(e.target.checked)}
                    />
                  }
                  label="Skip existing files"
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            size="large"
            startIcon={isRunning ? <StopIcon /> : <StartIcon />}
            onClick={isRunning ? onStop : handleStart}
            color={isRunning ? 'error' : 'primary'}
            disabled={!input.trim() || !outputDir.trim()}
          >
            {isRunning ? 'Stop Download' : 'Start Download'}
          </Button>

          <Button
            variant="outlined"
            size="large"
            startIcon={<PreviewIcon />}
            onClick={handlePreview}
            disabled={!input.trim() || isRunning}
          >
            Preview Tracks
          </Button>
        </Box>

        {/* Help Dialog */}
        <InputTypeHelp
          open={helpOpen}
          onClose={() => setHelpOpen(false)}
          inputType={inputType}
          downloadMode={downloadMode}
        />
      </CardContent>
    </Card>
  )
}

export default InputConfigForm
