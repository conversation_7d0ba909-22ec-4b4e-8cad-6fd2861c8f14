import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  LinearProgress,
  Alert,
} from '@mui/material'
import {
  Close as CloseIcon,
} from '@mui/icons-material'

import InteractiveAlbumSelector from './InteractiveAlbumSelector'

interface Track {
  id: string
  filename: string
  title: string
  artist: string
  album: string
  duration: number
  bitrate: number
  fileSize: number
  username: string
  selected: boolean
}

interface AlbumFolder {
  id: string
  path: string
  username: string
  tracks: Track[]
  totalSize: number
  averageBitrate: number
}

interface InteractiveAlbumDialogProps {
  open: boolean
  onClose: () => void
  searchQuery: string
  onDownload: (folderId: string, selectedTrackIds: string[]) => void
  onStartSearch: (query: string) => void
}

const InteractiveAlbumDialog: React.FC<InteractiveAlbumDialogProps> = ({
  open,
  onClose,
  searchQuery,
  onDownload,
  onStartSearch,
}) => {
  const [folders, setFolders] = useState<AlbumFolder[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (open && searchQuery) {
      handleSearch()
    }
  }, [open, searchQuery])

  const handleSearch = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // TODO: Implement actual search logic
      // This would call the backend to search for albums
      onStartSearch(searchQuery)
      
      // Simulate search results for now
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock data for demonstration
      const mockFolders: AlbumFolder[] = [
        {
          id: '1',
          path: '/Music/Artist - Album (2023) [FLAC]',
          username: 'musiclover123',
          totalSize: 450 * 1024 * 1024, // 450 MB
          averageBitrate: 1411,
          tracks: [
            {
              id: '1-1',
              filename: '01 - Track One.flac',
              title: 'Track One',
              artist: 'Artist',
              album: 'Album',
              duration: 245,
              bitrate: 1411,
              fileSize: 45 * 1024 * 1024,
              username: 'musiclover123',
              selected: false,
            },
            {
              id: '1-2',
              filename: '02 - Track Two.flac',
              title: 'Track Two',
              artist: 'Artist',
              album: 'Album',
              duration: 198,
              bitrate: 1411,
              fileSize: 38 * 1024 * 1024,
              username: 'musiclover123',
              selected: false,
            },
            {
              id: '1-3',
              filename: '03 - Track Three.flac',
              title: 'Track Three',
              artist: 'Artist',
              album: 'Album',
              duration: 312,
              bitrate: 1411,
              fileSize: 58 * 1024 * 1024,
              username: 'musiclover123',
              selected: false,
            },
          ],
        },
        {
          id: '2',
          path: '/Music/Artist - Album (2023) [MP3 320]',
          username: 'audiophile456',
          totalSize: 120 * 1024 * 1024, // 120 MB
          averageBitrate: 320,
          tracks: [
            {
              id: '2-1',
              filename: '01. Track One.mp3',
              title: 'Track One',
              artist: 'Artist',
              album: 'Album',
              duration: 245,
              bitrate: 320,
              fileSize: 12 * 1024 * 1024,
              username: 'audiophile456',
              selected: false,
            },
            {
              id: '2-2',
              filename: '02. Track Two.mp3',
              title: 'Track Two',
              artist: 'Artist',
              album: 'Album',
              duration: 198,
              bitrate: 320,
              fileSize: 10 * 1024 * 1024,
              username: 'audiophile456',
              selected: false,
            },
            {
              id: '2-3',
              filename: '03. Track Three.mp3',
              title: 'Track Three',
              artist: 'Artist',
              album: 'Album',
              duration: 312,
              bitrate: 320,
              fileSize: 15 * 1024 * 1024,
              username: 'audiophile456',
              selected: false,
            },
          ],
        },
      ]
      
      setFolders(mockFolders)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search for albums')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownload = (folderId: string, selectedTrackIds: string[]) => {
    onDownload(folderId, selectedTrackIds)
    // Keep dialog open to allow for more downloads
  }

  const handleRefresh = () => {
    handleSearch()
  }

  const handleClose = () => {
    setFolders([])
    setError(null)
    onClose()
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">
            Interactive Album Selection
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Search: "{searchQuery}"
          </Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
        <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {isLoading && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <LinearProgress sx={{ mb: 2 }} />
              <Typography>Searching for albums...</Typography>
              <Typography variant="body2" color="text.secondary">
                This may take a few moments while we search the Soulseek network
              </Typography>
            </Box>
          )}
          
          {!isLoading && folders.length === 0 && !error && (
            <Alert severity="info">
              No album folders found for this search. Try a different search query or check your connection.
            </Alert>
          )}
          
          {!isLoading && folders.length > 0 && (
            <InteractiveAlbumSelector
              folders={folders}
              onDownload={handleDownload}
              onClose={handleClose}
              onRefresh={handleRefresh}
              isLoading={isLoading}
            />
          )}
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose} startIcon={<CloseIcon />}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default InteractiveAlbumDialog
