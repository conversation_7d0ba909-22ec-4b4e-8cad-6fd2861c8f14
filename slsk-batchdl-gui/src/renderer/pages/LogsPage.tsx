import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Divider,
  Alert,
  Tooltip,
  ListItemIcon,
} from '@mui/material'
import {
  Clear as ClearIcon,
  Download as DownloadIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  BugReport as BugReportIcon,
  <PERSON>rror as ErrorIcon,
  Warning as WarningIcon,
  CheckCircle as SuccessIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'

import ErrorReportDialog from '../components/ErrorReportDialog'
import { useNotifications } from '../components/NotificationSystem'

// Mock log data
const mockLogs = [
  {
    id: '1',
    timestamp: '2024-01-15 14:30:25',
    level: 'info',
    message: 'Connected to Soulseek server',
    source: 'connection',
  },
  {
    id: '2',
    timestamp: '2024-01-15 14:30:30',
    level: 'info',
    message: 'Starting search for: Artist - Song Title',
    source: 'search',
  },
  {
    id: '3',
    timestamp: '2024-01-15 14:30:35',
    level: 'success',
    message: 'Found 15 search results',
    source: 'search',
  },
  {
    id: '4',
    timestamp: '2024-01-15 14:30:40',
    level: 'info',
    message: 'Starting download: song.mp3',
    source: 'download',
  },
  {
    id: '5',
    timestamp: '2024-01-15 14:30:45',
    level: 'warning',
    message: 'Download speed is slower than expected',
    source: 'download',
  },
  {
    id: '6',
    timestamp: '2024-01-15 14:31:00',
    level: 'success',
    message: 'Download completed: song.mp3',
    source: 'download',
  },
  {
    id: '7',
    timestamp: '2024-01-15 14:31:05',
    level: 'error',
    message: 'Failed to download: another_song.mp3 - User offline',
    source: 'download',
  },
]

const LogsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [levelFilter, setLevelFilter] = useState('all')
  const [sourceFilter, setSourceFilter] = useState('all')

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'error'
      case 'warning':
        return 'warning'
      case 'success':
        return 'success'
      case 'info':
      default:
        return 'primary'
    }
  }

  const filteredLogs = mockLogs.filter(log => {
    const matchesSearch = log.message.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesLevel = levelFilter === 'all' || log.level === levelFilter
    const matchesSource = sourceFilter === 'all' || log.source === sourceFilter
    return matchesSearch && matchesLevel && matchesSource
  })

  const handleClearLogs = () => {
    console.log('Clear logs')
  }

  const handleExportLogs = () => {
    console.log('Export logs')
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Logs
      </Typography>

      <Typography variant="body1" color="text.secondary" paragraph>
        View application logs and debug information
      </Typography>

      {/* Log Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              label="Search logs"
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
              }}
              sx={{ minWidth: 200 }}
            />

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Level</InputLabel>
              <Select
                value={levelFilter}
                label="Level"
                onChange={(e) => setLevelFilter(e.target.value)}
              >
                <MenuItem value="all">All Levels</MenuItem>
                <MenuItem value="info">Info</MenuItem>
                <MenuItem value="success">Success</MenuItem>
                <MenuItem value="warning">Warning</MenuItem>
                <MenuItem value="error">Error</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Source</InputLabel>
              <Select
                value={sourceFilter}
                label="Source"
                onChange={(e) => setSourceFilter(e.target.value)}
              >
                <MenuItem value="all">All Sources</MenuItem>
                <MenuItem value="connection">Connection</MenuItem>
                <MenuItem value="search">Search</MenuItem>
                <MenuItem value="download">Download</MenuItem>
              </Select>
            </FormControl>

            <Box sx={{ flexGrow: 1 }} />

            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExportLogs}
              size="small"
            >
              Export
            </Button>

            <Button
              variant="outlined"
              startIcon={<ClearIcon />}
              onClick={handleClearLogs}
              size="small"
              color="error"
            >
              Clear
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Log Statistics */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Log Summary
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Chip
              label={`Total: ${mockLogs.length}`}
              color="default"
              variant="outlined"
            />
            <Chip
              label={`Errors: ${mockLogs.filter(l => l.level === 'error').length}`}
              color="error"
              variant="outlined"
            />
            <Chip
              label={`Warnings: ${mockLogs.filter(l => l.level === 'warning').length}`}
              color="warning"
              variant="outlined"
            />
            <Chip
              label={`Success: ${mockLogs.filter(l => l.level === 'success').length}`}
              color="success"
              variant="outlined"
            />
          </Box>
        </CardContent>
      </Card>

      {/* Log Entries */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Log Entries ({filteredLogs.length})
          </Typography>

          <Paper variant="outlined" sx={{ maxHeight: 600, overflow: 'auto' }}>
            <List dense>
              {filteredLogs.map((log, index) => (
                <React.Fragment key={log.id}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            label={log.level.toUpperCase()}
                            color={getLevelColor(log.level)}
                            size="small"
                            variant="outlined"
                          />
                          <Typography variant="body2" component="span">
                            {log.message}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                          <Typography variant="caption" color="text.secondary">
                            {log.timestamp}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {log.source}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < filteredLogs.length - 1 && <Divider />}
                </React.Fragment>
              ))}

              {filteredLogs.length === 0 && (
                <ListItem>
                  <ListItemText
                    primary="No logs match the current filters"
                    sx={{ textAlign: 'center', color: 'text.secondary' }}
                  />
                </ListItem>
              )}
            </List>
          </Paper>
        </CardContent>
      </Card>
    </Box>
  )
}

export default LogsPage
