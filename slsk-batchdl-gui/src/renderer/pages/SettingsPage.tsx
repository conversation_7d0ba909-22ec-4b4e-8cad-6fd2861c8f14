import React, { useState } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Alert,
  <PERSON>bs,
  <PERSON>b,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  MenuItem,
} from '@mui/material'
import {
  Save as SaveIcon,
  Restore as RestoreIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material'

import FileConditionsForm from '../components/FileConditionsForm'
import DownloadSettingsForm from '../components/DownloadSettingsForm'
import ConnectionManager from '../components/ConnectionManager'
import ApiCredentialsManager from '../components/ApiCredentialsManager'
import { FileConditions } from '../types/slsk'

interface TabPanelProps {
  children?: React.ReactNode
  index: number
  value: number
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  )
}

const SettingsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0)

  // Settings state
  const [necessaryConditions, setNecessaryConditions] = useState<FileConditions>({})
  const [preferredConditions, setPreferredConditions] = useState<FileConditions>({})
  const [downloadSettings, setDownloadSettings] = useState({
    concurrentProcesses: 2,
    searchTimeout: 6000,
    maxStaleTime: 50000,
    searchesPerTime: 34,
    searchRenewTime: 220,
    desperateSearch: false,
    fastSearch: false,
    artistMaybeWrong: false,
    removeFt: false,
    skipExisting: true,
    writePlaylist: true,
    writeIndex: false,
    noProgress: false,
  })

  // API Credentials state
  const [apiCredentials, setApiCredentials] = useState({
    spotify: {
      clientId: '',
      clientSecret: '',
      accessToken: '',
      refreshToken: '',
      isValid: false,
      lastValidated: null as Date | null,
    },
    youtube: {
      apiKey: '',
      isValid: false,
      lastValidated: null as Date | null,
    },
  })

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  // Connection handlers
  const handleConnect = (service: string, credentials: any) => {
    console.log(`Connecting to ${service}:`, credentials)
    // TODO: Implement actual connection logic
  }

  const handleDisconnect = (service: string) => {
    console.log(`Disconnecting from ${service}`)
    // TODO: Implement actual disconnection logic
  }

  const handleTestConnection = (service: string) => {
    console.log(`Testing connection to ${service}`)
    // TODO: Implement connection testing
  }

  // API Credentials handlers
  const handleCredentialsChange = (service: string, field: string, value: any) => {
    setApiCredentials(prev => ({
      ...prev,
      [service]: {
        ...prev[service as keyof typeof prev],
        [field]: value,
      },
    }))
  }

  const handleValidateCredentials = (service: string) => {
    console.log(`Validating credentials for ${service}`)
    // TODO: Implement credential validation
    setApiCredentials(prev => ({
      ...prev,
      [service]: {
        ...prev[service as keyof typeof prev],
        isValid: true,
        lastValidated: new Date(),
      },
    }))
  }

  const handleAuthorizeSpotify = () => {
    console.log('Starting Spotify authorization flow')
    // TODO: Implement Spotify OAuth flow
  }

  const handleSaveSettings = () => {
    // TODO: Implement settings save
    console.log('Saving settings:', {
      necessaryConditions,
      preferredConditions,
      downloadSettings,
      apiCredentials,
    })
  }

  const handleResetSettings = () => {
    // TODO: Implement settings reset
    console.log('Resetting settings to defaults')
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Settings
      </Typography>

      <Typography variant="body1" color="text.secondary" paragraph>
        Configure your download preferences and connection settings
      </Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Connection" />
          <Tab label="File Conditions" />
          <Tab label="Download Settings" />
          <Tab label="External APIs" />
          <Tab label="Advanced" />
        </Tabs>
      </Box>

      {/* Connection Tab */}
      <TabPanel value={tabValue} index={0}>
        <ConnectionManager
          onConnect={handleConnect}
          onDisconnect={handleDisconnect}
          onTestConnection={handleTestConnection}
        />
      </TabPanel>

      {/* File Conditions Tab */}
      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <FileConditionsForm
              title="Necessary Conditions"
              conditions={necessaryConditions}
              onChange={setNecessaryConditions}
              isNecessary={true}
            />
          </Grid>

          <Grid item xs={12}>
            <FileConditionsForm
              title="Preferred Conditions"
              conditions={preferredConditions}
              onChange={setPreferredConditions}
              isNecessary={false}
            />
          </Grid>
        </Grid>
      </TabPanel>

      {/* Download Settings Tab */}
      <TabPanel value={tabValue} index={2}>
        <DownloadSettingsForm
          settings={downloadSettings}
          onChange={setDownloadSettings}
        />
      </TabPanel>

      {/* External APIs Tab */}
      <TabPanel value={tabValue} index={3}>
        <ApiCredentialsManager
          credentials={apiCredentials}
          onCredentialsChange={handleCredentialsChange}
          onValidateCredentials={handleValidateCredentials}
          onAuthorizeSpotify={handleAuthorizeSpotify}
        />
      </TabPanel>

      {/* Advanced Tab */}
      <TabPanel value={tabValue} index={4}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  File Naming & Organization
                </Typography>

                <TextField
                  fullWidth
                  label="Name Format Template"
                  placeholder="{artist} - {title}"
                  sx={{ mb: 2 }}
                  helperText="Available variables: {artist}, {title}, {album}, {year}, {track}"
                />

                <TextField
                  fullWidth
                  label="Album Art Option"
                  select
                  defaultValue="default"
                  sx={{ mb: 2 }}
                >
                  <MenuItem value="default">Default</MenuItem>
                  <MenuItem value="largest">Largest</MenuItem>
                  <MenuItem value="most">Most Common</MenuItem>
                </TextField>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Advanced Search Options
                </Typography>

                <TextField
                  fullWidth
                  label="yt-dlp Arguments"
                  placeholder="--extract-flat-playlist"
                  sx={{ mb: 2 }}
                  helperText="Additional arguments for yt-dlp fallback"
                />

                <TextField
                  fullWidth
                  type="number"
                  label="Album Track Count"
                  defaultValue="0"
                  sx={{ mb: 2 }}
                  helperText="Minimum tracks required for album downloads (0 = auto)"
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
        <Button
          variant="outlined"
          startIcon={<RestoreIcon />}
          onClick={handleResetSettings}
        >
          Reset to Defaults
        </Button>

        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSaveSettings}
        >
          Save Settings
        </Button>
      </Box>
    </Box>
  )
}

export default SettingsPage
