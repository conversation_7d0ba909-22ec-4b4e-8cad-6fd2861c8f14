import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@mui/material'
import {
  PlayArrow as PlayIcon,
} from '@mui/icons-material'

import InteractiveAlbumSelector from '../components/InteractiveAlbumSelector'

// Mock data for demonstration
const mockFolders = [
  {
    id: '1',
    path: '/Music/Pink Floyd - The Dark Side of the Moon (1973) [FLAC]',
    username: 'vinylcollector',
    totalSize: 380 * 1024 * 1024, // 380 MB
    averageBitrate: 1411,
    tracks: [
      {
        id: '1-1',
        filename: '01 - Speak to Me.flac',
        title: 'Speak to Me',
        artist: '<PERSON> Floyd',
        album: 'The Dark Side of the Moon',
        duration: 68,
        bitrate: 1411,
        fileSize: 12 * 1024 * 1024,
        username: 'vinylcollector',
        selected: false,
      },
      {
        id: '1-2',
        filename: '02 - <PERSON>reath<PERSON> (In the Air).flac',
        title: '<PERSON>reath<PERSON> (In the Air)',
        artist: '<PERSON> Floyd',
        album: 'The Dark Side of the Moon',
        duration: 163,
        bitrate: 1411,
        fileSize: 28 * 1024 * 1024,
        username: 'vinylcollector',
        selected: false,
      },
      {
        id: '1-3',
        filename: '03 - On the Run.flac',
        title: 'On the Run',
        artist: '<PERSON> Floyd',
        album: 'The Dark Side of the Moon',
        duration: 216,
        bitrate: 1411,
        fileSize: 37 * 1024 * 1024,
        username: 'vinylcollector',
        selected: false,
      },
      {
        id: '1-4',
        filename: '04 - Time.flac',
        title: 'Time',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon',
        duration: 413,
        bitrate: 1411,
        fileSize: 71 * 1024 * 1024,
        username: 'vinylcollector',
        selected: false,
      },
      {
        id: '1-5',
        filename: '05 - The Great Gig in the Sky.flac',
        title: 'The Great Gig in the Sky',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon',
        duration: 283,
        bitrate: 1411,
        fileSize: 48 * 1024 * 1024,
        username: 'vinylcollector',
        selected: false,
      },
    ],
  },
  {
    id: '2',
    path: '/Music/Pink Floyd - The Dark Side of the Moon [MP3 320]',
    username: 'musicfan2023',
    totalSize: 95 * 1024 * 1024, // 95 MB
    averageBitrate: 320,
    tracks: [
      {
        id: '2-1',
        filename: '01. Speak to Me.mp3',
        title: 'Speak to Me',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon',
        duration: 68,
        bitrate: 320,
        fileSize: 3 * 1024 * 1024,
        username: 'musicfan2023',
        selected: false,
      },
      {
        id: '2-2',
        filename: '02. Breathe (In the Air).mp3',
        title: 'Breathe (In the Air)',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon',
        duration: 163,
        bitrate: 320,
        fileSize: 7 * 1024 * 1024,
        username: 'musicfan2023',
        selected: false,
      },
      {
        id: '2-3',
        filename: '03. On the Run.mp3',
        title: 'On the Run',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon',
        duration: 216,
        bitrate: 320,
        fileSize: 9 * 1024 * 1024,
        username: 'musicfan2023',
        selected: false,
      },
      {
        id: '2-4',
        filename: '04. Time.mp3',
        title: 'Time',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon',
        duration: 413,
        bitrate: 320,
        fileSize: 17 * 1024 * 1024,
        username: 'musicfan2023',
        selected: false,
      },
      {
        id: '2-5',
        filename: '05. The Great Gig in the Sky.mp3',
        title: 'The Great Gig in the Sky',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon',
        duration: 283,
        bitrate: 320,
        fileSize: 12 * 1024 * 1024,
        username: 'musicfan2023',
        selected: false,
      },
    ],
  },
  {
    id: '3',
    path: '/Albums/Pink Floyd/Dark Side of the Moon (Remaster)',
    username: 'audiophile_supreme',
    totalSize: 420 * 1024 * 1024, // 420 MB
    averageBitrate: 1411,
    tracks: [
      {
        id: '3-1',
        filename: 'Track01 - Speak to Me.flac',
        title: 'Speak to Me',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon (Remaster)',
        duration: 68,
        bitrate: 1411,
        fileSize: 12 * 1024 * 1024,
        username: 'audiophile_supreme',
        selected: false,
      },
      {
        id: '3-2',
        filename: 'Track02 - Breathe.flac',
        title: 'Breathe',
        artist: 'Pink Floyd',
        album: 'The Dark Side of the Moon (Remaster)',
        duration: 163,
        bitrate: 1411,
        fileSize: 28 * 1024 * 1024,
        username: 'audiophile_supreme',
        selected: false,
      },
    ],
  },
]

const InteractiveDemoPage: React.FC = () => {
  const [showDemo, setShowDemo] = useState(false)

  const handleDownload = (folderId: string, selectedTrackIds: string[]) => {
    console.log('Demo download:', { folderId, selectedTrackIds })
    alert(`Demo: Would download ${selectedTrackIds.length} tracks from folder ${folderId}`)
  }

  const handleRefresh = () => {
    console.log('Demo refresh')
    alert('Demo: Would refresh the search results')
  }

  const handleClose = () => {
    setShowDemo(false)
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Interactive Album Selection Demo
      </Typography>

      <Typography variant="body1" color="text.secondary" paragraph>
        This page demonstrates the interactive album selection interface that allows users to browse
        through different album folders and select specific tracks to download.
      </Typography>

      {!showDemo ? (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Demo Features
            </Typography>
            
            <Alert severity="info" sx={{ mb: 2 }}>
              This demo shows how users can interactively browse album folders found during a search
              and select specific tracks to download instead of downloading entire albums.
            </Alert>

            <Typography variant="body2" paragraph>
              <strong>Features demonstrated:</strong>
            </Typography>
            <ul>
              <li>Browse through multiple album folders from different users</li>
              <li>View detailed track information (duration, bitrate, file size)</li>
              <li>Filter folders by content</li>
              <li>Select individual tracks or entire albums</li>
              <li>Compare different versions (FLAC vs MP3, different bitrates)</li>
              <li>See folder paths and user information</li>
            </ul>

            <Button
              variant="contained"
              size="large"
              startIcon={<PlayIcon />}
              onClick={() => setShowDemo(true)}
              sx={{ mt: 2 }}
            >
              Start Interactive Demo
            </Button>
          </CardContent>
        </Card>
      ) : (
        <InteractiveAlbumSelector
          folders={mockFolders}
          onDownload={handleDownload}
          onClose={handleClose}
          onRefresh={handleRefresh}
          isLoading={false}
        />
      )}
    </Box>
  )
}

export default InteractiveDemoPage
