import React, { useState } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Paper,
  Alert,
} from '@mui/material'
import {
  CloudDownload as DownloadIcon,
  MusicNote as MusicIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
} from '@mui/icons-material'

import InputConfigForm from '../components/InputConfigForm'
import InteractiveAlbumDialog from '../components/InteractiveAlbumDialog'

const HomePage: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false)
  const [stats, setStats] = useState({
    totalDownloads: 0,
    activeDownloads: 0,
    completedDownloads: 0,
    failedDownloads: 0,
  })
  const [interactiveDialogOpen, setInteractiveDialogOpen] = useState(false)
  const [currentSearchQuery, setCurrentSearchQuery] = useState('')

  const handleStart = (config: any) => {
    console.log('Starting download with config:', config)

    if (config.downloadMode === 'interactive') {
      // Open interactive album selection dialog
      setCurrentSearchQuery(config.input)
      setInteractiveDialogOpen(true)
    } else {
      // Start normal download
      setIsRunning(true)
      // TODO: Implement actual start logic with slsk process
    }
  }

  const handlePreview = (config: any) => {
    console.log('Previewing tracks with config:', config)
    // TODO: Implement preview logic
  }

  const handleStop = () => {
    setIsRunning(false)
    console.log('Stopping download')
    // TODO: Implement actual stop logic
  }

  const handleInteractiveDownload = (folderId: string, selectedTrackIds: string[]) => {
    console.log('Starting interactive download:', { folderId, selectedTrackIds })
    // TODO: Implement actual download logic for selected tracks
    setStats(prev => ({
      ...prev,
      activeDownloads: prev.activeDownloads + selectedTrackIds.length,
      totalDownloads: prev.totalDownloads + selectedTrackIds.length,
    }))
  }

  const handleStartSearch = (query: string) => {
    console.log('Starting search for:', query)
    // TODO: Implement actual search logic
  }

  const handleCloseInteractiveDialog = () => {
    setInteractiveDialogOpen(false)
    setCurrentSearchQuery('')
  }



  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Welcome to SLSK BatchDL
      </Typography>

      <Typography variant="body1" color="text.secondary" paragraph>
        A modern GUI for downloading music from Soulseek. Choose your input source and start downloading!
      </Typography>

      <Grid container spacing={3}>
        {/* Main Input Section */}
        <Grid item xs={12} md={8}>
          <InputConfigForm
            onStart={handleStart}
            onPreview={handlePreview}
            isRunning={isRunning}
            onStop={handleStop}
          />
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={4}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <MusicIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h6">{stats.totalDownloads}</Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Downloads
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={6}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <DownloadIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h6">{stats.activeDownloads}</Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Downloads
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={6}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <SuccessIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h6">{stats.completedDownloads}</Typography>
                <Typography variant="body2" color="text.secondary">
                  Completed
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={6}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <ErrorIcon color="error" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="h6">{stats.failedDownloads}</Typography>
                <Typography variant="body2" color="text.secondary">
                  Failed
                </Typography>
              </Paper>
            </Grid>
          </Grid>

          {/* Status Alert */}
          {isRunning && (
            <Alert severity="info" sx={{ mt: 2 }}>
              Download in progress... Check the Downloads page for detailed progress.
            </Alert>
          )}

          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Chip
                  label="Open Settings"
                  clickable
                  variant="outlined"
                  onClick={() => console.log('Open settings')}
                />
                <Chip
                  label="View Download Queue"
                  clickable
                  variant="outlined"
                  onClick={() => console.log('View queue')}
                />
                <Chip
                  label="Check Connection"
                  clickable
                  variant="outlined"
                  onClick={() => console.log('Check connection')}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Interactive Album Selection Dialog */}
      <InteractiveAlbumDialog
        open={interactiveDialogOpen}
        onClose={handleCloseInteractiveDialog}
        searchQuery={currentSearchQuery}
        onDownload={handleInteractiveDownload}
        onStartSearch={handleStartSearch}
      />
    </Box>
  )
}

export default HomePage
