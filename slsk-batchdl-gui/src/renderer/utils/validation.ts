import { InputType } from '../types/slsk'

export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

export const validateInput = (input: string, inputType: InputType): ValidationResult => {
  const errors: Record<string, string> = {}

  if (!input.trim()) {
    errors.input = 'Input is required'
    return { isValid: false, errors }
  }

  switch (inputType) {
    case 'spotify':
      if (!validateSpotifyInput(input)) {
        errors.input = 'Please enter a valid Spotify URL or "spotify-likes"'
      }
      break

    case 'youtube':
      if (!validateYouTubeInput(input)) {
        errors.input = 'Please enter a valid YouTube playlist URL'
      }
      break

    case 'csv':
      if (!validateCsvInput(input)) {
        errors.input = 'Please select a valid CSV file'
      }
      break

    case 'bandcamp':
      if (!validateBandcampInput(input)) {
        errors.input = 'Please enter a valid Bandcamp URL'
      }
      break

    case 'string':
      if (!validateStringInput(input)) {
        errors.input = 'Please enter a valid search query'
      }
      break

    default:
      errors.input = 'Invalid input type'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

const validateSpotifyInput = (input: string): boolean => {
  const trimmed = input.trim().toLowerCase()
  
  // Check for spotify-likes
  if (trimmed === 'spotify-likes') {
    return true
  }

  // Check for Spotify URLs
  const spotifyUrlPatterns = [
    /^https?:\/\/open\.spotify\.com\/playlist\/[a-zA-Z0-9]+/,
    /^https?:\/\/open\.spotify\.com\/album\/[a-zA-Z0-9]+/,
    /^https?:\/\/open\.spotify\.com\/artist\/[a-zA-Z0-9]+/,
    /^spotify:playlist:[a-zA-Z0-9]+/,
    /^spotify:album:[a-zA-Z0-9]+/,
    /^spotify:artist:[a-zA-Z0-9]+/,
  ]

  return spotifyUrlPatterns.some(pattern => pattern.test(input))
}

const validateYouTubeInput = (input: string): boolean => {
  const youtubeUrlPatterns = [
    /^https?:\/\/(www\.)?youtube\.com\/playlist\?list=[a-zA-Z0-9_-]+/,
    /^https?:\/\/(www\.)?youtube\.com\/watch\?v=[a-zA-Z0-9_-]+&list=[a-zA-Z0-9_-]+/,
    /^https?:\/\/youtu\.be\/[a-zA-Z0-9_-]+\?list=[a-zA-Z0-9_-]+/,
  ]

  return youtubeUrlPatterns.some(pattern => pattern.test(input))
}

const validateCsvInput = (input: string): boolean => {
  // Check if it's a file path ending with .csv
  return input.trim().toLowerCase().endsWith('.csv')
}

const validateBandcampInput = (input: string): boolean => {
  const bandcampUrlPatterns = [
    /^https?:\/\/[a-zA-Z0-9-]+\.bandcamp\.com/,
    /^https?:\/\/bandcamp\.com\/[a-zA-Z0-9-]+/,
  ]

  return bandcampUrlPatterns.some(pattern => pattern.test(input))
}

const validateStringInput = (input: string): boolean => {
  const trimmed = input.trim()
  
  // Must be at least 2 characters
  if (trimmed.length < 2) {
    return false
  }

  // Check for basic search patterns
  // Can be "Artist - Title", "Artist, Title", or just a search term
  return true // Most string inputs are valid for search
}

export const validateOutputDirectory = (path: string): ValidationResult => {
  const errors: Record<string, string> = {}

  if (!path.trim()) {
    errors.outputDir = 'Output directory is required'
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

export const validateNumericInput = (
  value: number,
  fieldName: string,
  min?: number,
  max?: number
): ValidationResult => {
  const errors: Record<string, string> = {}

  if (isNaN(value)) {
    errors[fieldName] = `${fieldName} must be a valid number`
  } else {
    if (min !== undefined && value < min) {
      errors[fieldName] = `${fieldName} must be at least ${min}`
    }
    if (max !== undefined && value > max) {
      errors[fieldName] = `${fieldName} must be at most ${max}`
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

export const getInputTypeHints = (inputType: InputType): string[] => {
  switch (inputType) {
    case 'spotify':
      return [
        'Use "spotify-likes" to download your liked songs',
        'Playlist URLs: https://open.spotify.com/playlist/...',
        'Album URLs: https://open.spotify.com/album/...',
        'Artist URLs: https://open.spotify.com/artist/...',
        'Requires Spotify API credentials for private playlists',
      ]

    case 'youtube':
      return [
        'Playlist URLs: https://youtube.com/playlist?list=...',
        'Video URLs with playlist: https://youtube.com/watch?v=...&list=...',
        'Use YouTube API key for better reliability',
        'Can retrieve deleted video titles with yt-dlp',
      ]

    case 'csv':
      return [
        'CSV file with track information',
        'Supported columns: Artist, Title, Album, Length',
        'Column names are auto-detected if using common names',
        'Use --artist-col, --title-col flags for custom columns',
      ]

    case 'bandcamp':
      return [
        'Album URLs: https://artist.bandcamp.com/album/...',
        'Track URLs: https://artist.bandcamp.com/track/...',
        'Artist URLs: https://artist.bandcamp.com (downloads discography)',
        'Supports both artist pages and individual releases',
      ]

    case 'string':
    default:
      return [
        'Search for tracks: "Artist - Song Title"',
        'Search for albums: "Artist - Album Name" (use album mode)',
        'Simple search: "song title" or "artist name"',
        'Use properties: "title=Song, artist=Artist, length=215"',
      ]
  }
}

export const getDownloadModeHints = (mode: string): string[] => {
  switch (mode) {
    case 'album':
      return [
        'Downloads entire folders/albums from Soulseek',
        'Includes non-audio files (artwork, etc.)',
        'Use interactive mode to browse and select',
        'Automatically sets album track count requirements',
      ]

    case 'aggregate':
      return [
        'Finds all distinct songs by an artist',
        'Groups results and downloads one of each',
        'Requires minimum 2 shares by default',
        'Good for discovering artist discographies',
      ]

    case 'album-aggregate':
      return [
        'Finds all distinct albums by an artist',
        'Downloads one copy of each unique album',
        'Useful for complete artist collections',
        'Combine with interactive mode for selection',
      ]

    case 'normal':
    default:
      return [
        'Downloads individual tracks',
        'One file per input entry',
        'Fastest for specific songs',
        'Default download mode',
      ]
  }
}
