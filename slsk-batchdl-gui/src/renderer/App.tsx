import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Box } from '@mui/material'

import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import SettingsPage from './pages/SettingsPage'
import DownloadsPage from './pages/DownloadsPage'
import LogsPage from './pages/LogsPage'
import InteractiveDemoPage from './pages/InteractiveDemoPage'

function App() {
  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <Layout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/downloads" element={<DownloadsPage />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="/logs" element={<LogsPage />} />
          <Route path="/demo" element={<InteractiveDemoPage />} />
        </Routes>
      </Layout>
    </Box>
  )
}

export default App
