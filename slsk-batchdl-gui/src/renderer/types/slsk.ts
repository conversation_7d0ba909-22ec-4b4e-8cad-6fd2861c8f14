// Types based on the slsk-batchdl configuration and models

export interface FileConditions {
  formats?: string[]
  lengthTolerance?: number
  minBitrate?: number
  maxBitrate?: number
  minSampleRate?: number
  maxSampleRate?: number
  minBitDepth?: number
  maxBitDepth?: number
  strictTitle?: boolean
  strictArtist?: boolean
  strictAlbum?: boolean
  bannedUsers?: string[]
}

export interface SlskConfig {
  // Input and output
  input: string
  inputType?: 'csv' | 'youtube' | 'spotify' | 'bandcamp' | 'string' | 'list'
  parentDir: string
  nameFormat: string

  // Authentication
  username: string
  password: string
  spotifyId: string
  spotifySecret: string
  spotifyToken: string
  spotifyRefresh: string
  ytKey: string

  // File conditions
  necessaryCond: FileConditions
  preferredCond: FileConditions

  // Download settings
  maxTracks: number
  offset: number
  reverse: boolean
  concurrentProcesses: number
  fastSearch: boolean
  album: boolean
  aggregate: boolean
  interactiveMode: boolean

  // Search settings
  searchTimeout: number
  maxStaleTime: number
  searchesPerTime: number
  searchRenewTime: number
  desperateSearch: boolean
  artistMaybeWrong: boolean
  removeFt: boolean

  // Output settings
  writePlaylist: boolean
  skipExisting: boolean
  writeIndex: boolean
  noProgress: boolean

  // Advanced settings
  useYtdlp: boolean
  ytdlpArgument: string
  getDeleted: boolean
  deletedOnly: boolean
  removeTracksFromSource: boolean
  listenPort: number

  // CSV specific
  artistCol: string
  titleCol: string
  albumCol: string
  lengthCol: string
  timeUnit: string
  ytParse: boolean

  // Album settings
  albumTrackCount: number
  albumArtOption: 'default' | 'largest' | 'most'
  albumArtOnly: boolean
  noBrowseFolder: boolean
  parallelAlbumSearch: boolean

  // Profile and config
  profile: string
  confPath: string
}

export interface Track {
  artist: string
  title: string
  album: string
  length: number
  uri: string
  downloadPath: string
  state: 'initial' | 'searching' | 'downloading' | 'completed' | 'failed'
  failureReason?: string
}

export interface DownloadProgress {
  trackId: string
  filename: string
  bytesTransferred: number
  totalBytes: number
  percentage: number
  speed: number
  eta: number
  state: 'queued' | 'downloading' | 'completed' | 'failed' | 'cancelled'
}

export interface SlskOutput {
  type: 'stdout' | 'stderr'
  data: string
  timestamp: Date
}

export interface SlskProcessResult {
  code: number
  stdout: string
  stderr: string
}

export interface ConfigProfile {
  name: string
  description: string
  config: Partial<SlskConfig>
}

export type InputType = 'csv' | 'youtube' | 'spotify' | 'bandcamp' | 'string' | 'list'
export type DownloadMode = 'normal' | 'album' | 'interactive' | 'aggregate' | 'album-aggregate'
export type AlbumArtOption = 'default' | 'largest' | 'most'
